---
# Demo: Shared Mount for Same StorageClass
# This example shows how multiple PVCs using the same StorageClass
# will share a single mount-pod when deployed on the same node

apiVersion: v1
kind: Namespace
metadata:
  name: shared-mount-demo

---
# NFS StorageClass for shared mounting
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: demo-nfs-shared
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "*************"
  path: "/exports/shared-data"
  nfsVersion: "4"
  mountFlags: "hard,intr"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# First PVC using the shared StorageClass
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shared-pvc-1
  namespace: shared-mount-demo
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: demo-nfs-shared

---
# Second PVC using the same StorageClass
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shared-pvc-2
  namespace: shared-mount-demo
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: demo-nfs-shared

---
# Third PVC using the same StorageClass
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shared-pvc-3
  namespace: shared-mount-demo
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: demo-nfs-shared

---
# Pod 1 using shared-pvc-1
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-1
  namespace: shared-mount-demo
  labels:
    app: shared-mount-demo
spec:
  # Force scheduling to a specific node (replace with your node name)
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # 替换为你的实际节点名
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod 1 started"
      echo "Writing to shared storage from Pod 1" > /data/pod1.txt
      echo "Content written by Pod 1: $(date)" >> /data/pod1.txt
      
      # Keep running and periodically check shared files
      while true; do
        echo "=== Pod 1 Status at $(date) ==="
        echo "Files in /data:"
        ls -la /data/
        echo "Content of all txt files:"
        cat /data/*.txt 2>/dev/null || echo "No txt files found"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-storage
      mountPath: /data
  volumes:
  - name: shared-storage
    persistentVolumeClaim:
      claimName: shared-pvc-1

---
# Pod 2 using shared-pvc-2
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-2
  namespace: shared-mount-demo
  labels:
    app: shared-mount-demo
spec:
  # Force scheduling to the same node as Pod 1
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # 替换为你的实际节点名
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod 2 started"
      sleep 10  # Wait a bit for Pod 1 to write
      echo "Writing to shared storage from Pod 2" > /data/pod2.txt
      echo "Content written by Pod 2: $(date)" >> /data/pod2.txt
      
      # Keep running and periodically check shared files
      while true; do
        echo "=== Pod 2 Status at $(date) ==="
        echo "Files in /data:"
        ls -la /data/
        echo "Content of all txt files:"
        cat /data/*.txt 2>/dev/null || echo "No txt files found"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-storage
      mountPath: /data
  volumes:
  - name: shared-storage
    persistentVolumeClaim:
      claimName: shared-pvc-2

---
# Pod 3 using shared-pvc-3
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-3
  namespace: shared-mount-demo
  labels:
    app: shared-mount-demo
spec:
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod 3 started"
      sleep 20  # Wait a bit for other pods to write
      echo "Writing to shared storage from Pod 3" > /data/pod3.txt
      echo "Content written by Pod 3: $(date)" >> /data/pod3.txt
      
      # Keep running and periodically check shared files
      while true; do
        echo "=== Pod 3 Status at $(date) ==="
        echo "Files in /data:"
        ls -la /data/
        echo "Content of all txt files:"
        cat /data/*.txt 2>/dev/null || echo "No txt files found"
        echo "Summary file created by Pod 3:"
        echo "Summary of all pods at $(date)" > /data/summary.txt
        echo "Pod 1 file exists: $(test -f /data/pod1.txt && echo 'YES' || echo 'NO')" >> /data/summary.txt
        echo "Pod 2 file exists: $(test -f /data/pod2.txt && echo 'YES' || echo 'NO')" >> /data/summary.txt
        echo "Pod 3 file exists: $(test -f /data/pod3.txt && echo 'YES' || echo 'NO')" >> /data/summary.txt
        cat /data/summary.txt
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-storage
      mountPath: /data
  volumes:
  - name: shared-storage
    persistentVolumeClaim:
      claimName: shared-pvc-3

---
# Job to demonstrate shared mount efficiency
apiVersion: batch/v1
kind: Job
metadata:
  name: mount-efficiency-check
  namespace: shared-mount-demo
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: checker
        image: busybox:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "=== Shared Mount Efficiency Check ==="
          echo "This job demonstrates that multiple PVCs using the same"
          echo "StorageClass parameters will share a single mount-pod"
          echo "instead of creating separate mount-pods for each PVC."
          echo ""
          echo "Expected behavior:"
          echo "1. Only ONE mount-pod should be created for the NFS mount"
          echo "2. All three PVCs should bind mount from the shared mount"
          echo "3. All pods should see the same files in /data"
          echo ""
          echo "To verify:"
          echo "kubectl get pods -n kube-system -l xpai-csi/mount-pod=true"
          echo "kubectl logs -n shared-mount-demo app-pod-1"
          echo "kubectl logs -n shared-mount-demo app-pod-2"
          echo "kubectl logs -n shared-mount-demo app-pod-3"
          echo ""
          echo "Check completed at $(date)"
