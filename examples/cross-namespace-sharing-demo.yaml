---
# Demo: Cross-Namespace PVC Sharing
# This example shows how PVCs with the same name in different namespaces
# can share the same underlying storage and mount the same NFS subdirectory
#
# IMPORTANT: Cross-namespace sharing only works for PVCs that:
# 1. Have the SAME PVC name
# 2. Use the SAME StorageClass
# 3. Have enableCrossNamespaceSharing=true in the StorageClass

# Create test namespaces
apiVersion: v1
kind: Namespace
metadata:
  name: app-namespace-a
---
apiVersion: v1
kind: Namespace
metadata:
  name: app-namespace-b
---
apiVersion: v1
kind: Namespace
metadata:
  name: app-namespace-c

---
# StorageClass with cross-namespace sharing enabled
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-data-cross-ns
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/shared-apps"  # Base path on NFS server
  nfsVersion: "4"
  mountFlags: "hard,intr"
  # Enable cross-namespace sharing for PVCs with same name
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# PVC named "app-data" in namespace-a
# This will create subdirectory: /exports/shared-apps/app-data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: app-namespace-a
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: shared-data-cross-ns

---
# PVC named "app-data" in namespace-b (same name!)
# This will share the same subdirectory: /exports/shared-apps/app-data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: app-namespace-b
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: shared-data-cross-ns

---
# PVC named "app-data" in namespace-c (same name!)
# This will also share the same subdirectory: /exports/shared-apps/app-data
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: app-namespace-c
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: shared-data-cross-ns

---
# Pod in namespace-a
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-a
  namespace: app-namespace-a
  labels:
    app: cross-ns-demo
spec:
  # Force scheduling to same node for demo
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # Replace with your node name
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod A in namespace app-namespace-a started"
      echo "Writing from namespace-a at $(date)" > /shared-data/from-namespace-a.txt
      echo "Namespace A was here: $(date)" >> /shared-data/shared-log.txt
      
      while true; do
        echo "=== Pod A Status at $(date) ==="
        echo "Files in shared directory:"
        ls -la /shared-data/
        echo "Content of shared log:"
        cat /shared-data/shared-log.txt 2>/dev/null || echo "No shared log yet"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-data
      mountPath: /shared-data
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data

---
# Pod in namespace-b
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-b
  namespace: app-namespace-b
  labels:
    app: cross-ns-demo
spec:
  # Force scheduling to same node for demo
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # Replace with your node name
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod B in namespace app-namespace-b started"
      sleep 10  # Wait a bit for Pod A
      echo "Writing from namespace-b at $(date)" > /shared-data/from-namespace-b.txt
      echo "Namespace B was here: $(date)" >> /shared-data/shared-log.txt
      
      while true; do
        echo "=== Pod B Status at $(date) ==="
        echo "Files in shared directory:"
        ls -la /shared-data/
        echo "Content of shared log:"
        cat /shared-data/shared-log.txt 2>/dev/null || echo "No shared log yet"
        echo "Can see file from namespace-a: $(test -f /shared-data/from-namespace-a.txt && echo 'YES' || echo 'NO')"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-data
      mountPath: /shared-data
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data

---
# Pod in namespace-c
apiVersion: v1
kind: Pod
metadata:
  name: app-pod-c
  namespace: app-namespace-c
  labels:
    app: cross-ns-demo
spec:
  # Force scheduling to same node for demo
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # Replace with your node name
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Pod C in namespace app-namespace-c started"
      sleep 20  # Wait for other pods
      echo "Writing from namespace-c at $(date)" > /shared-data/from-namespace-c.txt
      echo "Namespace C was here: $(date)" >> /shared-data/shared-log.txt
      
      while true; do
        echo "=== Pod C Status at $(date) ==="
        echo "Files in shared directory:"
        ls -la /shared-data/
        echo "Content of shared log:"
        cat /shared-data/shared-log.txt 2>/dev/null || echo "No shared log yet"
        echo "Can see files from other namespaces:"
        echo "  - namespace-a: $(test -f /shared-data/from-namespace-a.txt && echo 'YES' || echo 'NO')"
        echo "  - namespace-b: $(test -f /shared-data/from-namespace-b.txt && echo 'YES' || echo 'NO')"
        echo "Creating summary file..."
        echo "Cross-namespace sharing summary at $(date)" > /shared-data/summary.txt
        echo "Files created by each namespace:" >> /shared-data/summary.txt
        ls -la /shared-data/from-namespace-*.txt >> /shared-data/summary.txt 2>/dev/null || echo "No namespace files found" >> /shared-data/summary.txt
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-data
      mountPath: /shared-data
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data

---
# Job to verify cross-namespace sharing
apiVersion: batch/v1
kind: Job
metadata:
  name: cross-ns-verification
  namespace: app-namespace-a
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: verifier
        image: busybox:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "=== Cross-Namespace Sharing Verification ==="
          echo "This job verifies that PVCs with the same name in different"
          echo "namespaces are sharing the same underlying storage."
          echo ""
          echo "Expected behavior:"
          echo "1. Only ONE mount-pod should be created for all three PVCs"
          echo "2. All three PVCs should mount the same NFS subdirectory: /exports/shared-apps/app-data"
          echo "3. Files created by pods in different namespaces should be visible to all"
          echo ""
          echo "Verification commands:"
          echo "# Check mount pods (should be only 1)"
          echo "kubectl get pods -n kube-system -l xpai-csi/mount-pod=true"
          echo ""
          echo "# Check PVCs in all namespaces"
          echo "kubectl get pvc app-data -n app-namespace-a"
          echo "kubectl get pvc app-data -n app-namespace-b"  
          echo "kubectl get pvc app-data -n app-namespace-c"
          echo ""
          echo "# Check pod logs to see shared files"
          echo "kubectl logs -n app-namespace-a app-pod-a"
          echo "kubectl logs -n app-namespace-b app-pod-b"
          echo "kubectl logs -n app-namespace-c app-pod-c"
          echo ""
          echo "# Check CSI driver logs for cross-namespace sharing"
          echo "kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i cross"
          echo ""
          echo "Verification completed at $(date)"
