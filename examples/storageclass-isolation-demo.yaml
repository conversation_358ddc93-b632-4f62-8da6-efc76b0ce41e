---
# Demo: StorageClass Isolation for Cross-Namespace Sharing
# This example demonstrates that PVCs with the same name but different
# StorageClasses will NOT share storage, ensuring proper isolation

apiVersion: v1
kind: Namespace
metadata:
  name: team-a
---
apiVersion: v1
kind: Namespace
metadata:
  name: team-b

---
# StorageClass for Team A - pointing to different NFS path
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: team-a-storage
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/team-a-data"  # Team A's dedicated path
  nfsVersion: "4"
  mountFlags: "hard,intr"
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer

---
# StorageClass for Team B - pointing to different NFS path
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: team-b-storage
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/team-b-data"  # Team B's dedicated path
  nfsVersion: "4"
  mountFlags: "hard,intr"
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer

---
# PVC named "app-data" in team-a using team-a-storage
# This will create: /exports/team-a-data/app-data/
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: team-a
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: team-a-storage  # Team A's StorageClass

---
# PVC named "app-data" in team-b using team-b-storage
# This will create: /exports/team-b-data/app-data/ (SEPARATE from team-a)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: team-b
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: team-b-storage  # Team B's StorageClass (DIFFERENT!)

---
# Pod in team-a
apiVersion: v1
kind: Pod
metadata:
  name: team-a-app
  namespace: team-a
  labels:
    team: team-a
spec:
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # Replace with your node
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Team A app started"
      echo "Team A secret data: $(date)" > /data/team-a-secret.txt
      echo "This is Team A's private data" > /data/team-a-private.txt
      
      while true; do
        echo "=== Team A Status at $(date) ==="
        echo "Files in Team A's storage:"
        ls -la /data/
        echo "Team A can only see its own files"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: app-data
      mountPath: /data
  volumes:
  - name: app-data
    persistentVolumeClaim:
      claimName: app-data

---
# Pod in team-b
apiVersion: v1
kind: Pod
metadata:
  name: team-b-app
  namespace: team-b
  labels:
    team: team-b
spec:
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"  # Same node as team-a
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Team B app started"
      echo "Team B secret data: $(date)" > /data/team-b-secret.txt
      echo "This is Team B's private data" > /data/team-b-private.txt
      
      while true; do
        echo "=== Team B Status at $(date) ==="
        echo "Files in Team B's storage:"
        ls -la /data/
        echo "Team B can only see its own files"
        echo "Team A's files should NOT be visible here"
        echo "Checking for team-a files:"
        echo "  team-a-secret.txt: $(test -f /data/team-a-secret.txt && echo 'FOUND (ERROR!)' || echo 'NOT FOUND (CORRECT)')"
        echo "  team-a-private.txt: $(test -f /data/team-a-private.txt && echo 'FOUND (ERROR!)' || echo 'NOT FOUND (CORRECT)')"
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: app-data
      mountPath: /data
  volumes:
  - name: app-data
    persistentVolumeClaim:
      claimName: app-data

---
# Verification Job
apiVersion: batch/v1
kind: Job
metadata:
  name: storageclass-isolation-verification
  namespace: team-a
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: verifier
        image: busybox:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "=== StorageClass Isolation Verification ==="
          echo "This job verifies that PVCs with the same name but different"
          echo "StorageClasses do NOT share storage, ensuring proper isolation."
          echo ""
          echo "Expected behavior:"
          echo "1. Two separate mount-pods should be created (one per StorageClass)"
          echo "2. Team A's PVC should mount: /exports/team-a-data/app-data/"
          echo "3. Team B's PVC should mount: /exports/team-b-data/app-data/"
          echo "4. Teams should NOT see each other's files"
          echo ""
          echo "Verification commands:"
          echo "# Check mount pods (should be 2, one per StorageClass)"
          echo "kubectl get pods -n kube-system -l xpai-csi/mount-pod=true"
          echo ""
          echo "# Check PVCs use different StorageClasses"
          echo "kubectl get pvc app-data -n team-a -o jsonpath='{.spec.storageClassName}'"
          echo "kubectl get pvc app-data -n team-b -o jsonpath='{.spec.storageClassName}'"
          echo ""
          echo "# Verify data isolation"
          echo "kubectl logs -n team-a team-a-app"
          echo "kubectl logs -n team-b team-b-app"
          echo ""
          echo "# Check CSI driver logs for StorageClass isolation"
          echo "kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i storageclass"
          echo ""
          echo "Key Points:"
          echo "- Same PVC name: 'app-data'"
          echo "- Different StorageClasses: 'team-a-storage' vs 'team-b-storage'"
          echo "- Result: NO sharing, proper isolation"
          echo ""
          echo "Verification completed at $(date)"

---
# Additional example: Same StorageClass, different PVC names (also no sharing)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: different-pvc-name
  namespace: team-a
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: team-a-storage  # Same StorageClass as team-a's app-data

---
apiVersion: v1
kind: Pod
metadata:
  name: different-name-test
  namespace: team-a
spec:
  nodeSelector:
    kubernetes.io/hostname: "worker-node-1"
  containers:
  - name: app
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Testing different PVC name with same StorageClass"
      echo "This PVC has a different name, so it should NOT share with 'app-data'"
      echo "Different PVC data: $(date)" > /data/different-pvc.txt
      
      while true; do
        echo "=== Different PVC Name Test at $(date) ==="
        echo "Files in this PVC (different-pvc-name):"
        ls -la /data/
        echo "Should NOT see files from 'app-data' PVC:"
        echo "  team-a-secret.txt: $(test -f /data/team-a-secret.txt && echo 'FOUND (ERROR!)' || echo 'NOT FOUND (CORRECT)')"
        echo "================================"
        sleep 60
      done
    volumeMounts:
    - name: data
      mountPath: /data
  volumes:
  - name: data
    persistentVolumeClaim:
      claimName: different-pvc-name
