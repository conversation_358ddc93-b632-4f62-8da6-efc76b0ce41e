---
# Test for Controller Server Cross-Namespace Sharing
# This example demonstrates how the controller server handles
# cross-namespace volume creation and validation

apiVersion: v1
kind: Namespace
metadata:
  name: controller-test-a
---
apiVersion: v1
kind: Namespace
metadata:
  name: controller-test-b

---
# StorageClass for controller testing
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: controller-test-storage
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/controller-test"
  nfsVersion: "4"
  mountFlags: "hard,intr"
  # Enable cross-namespace sharing
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# PVC in namespace-a (will trigger CreateVolume in controller)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shared-volume
  namespace: controller-test-a
spec:
  accessModes:
    - ReadWriteMany  # Test multi-node access mode
  resources:
    requests:
      storage: 1Gi
  storageClassName: controller-test-storage

---
# PVC in namespace-b with same name (should get same volume ID)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: shared-volume
  namespace: controller-test-b
spec:
  accessModes:
    - ReadWriteMany  # Test multi-node access mode
  resources:
    requests:
      storage: 1Gi
  storageClassName: controller-test-storage

---
# Pod in namespace-a to test the volume
apiVersion: v1
kind: Pod
metadata:
  name: controller-test-pod-a
  namespace: controller-test-a
spec:
  containers:
  - name: test
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Controller test pod A started"
      echo "Volume ID from controller: $(cat /proc/mounts | grep /data | awk '{print $1}')"
      echo "Testing cross-namespace sharing from controller..."
      echo "Pod A data: $(date)" > /data/pod-a.txt
      
      while true; do
        echo "=== Controller Test Pod A at $(date) ==="
        echo "Files in shared volume:"
        ls -la /data/
        echo "Volume mount info:"
        mount | grep /data
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-volume
      mountPath: /data
  volumes:
  - name: shared-volume
    persistentVolumeClaim:
      claimName: shared-volume

---
# Pod in namespace-b to test the volume
apiVersion: v1
kind: Pod
metadata:
  name: controller-test-pod-b
  namespace: controller-test-b
spec:
  containers:
  - name: test
    image: busybox:latest
    command:
    - /bin/sh
    - -c
    - |
      echo "Controller test pod B started"
      echo "Volume ID from controller: $(cat /proc/mounts | grep /data | awk '{print $1}')"
      echo "Testing cross-namespace sharing from controller..."
      sleep 10  # Wait for pod A
      echo "Pod B data: $(date)" > /data/pod-b.txt
      
      while true; do
        echo "=== Controller Test Pod B at $(date) ==="
        echo "Files in shared volume:"
        ls -la /data/
        echo "Can see Pod A's file: $(test -f /data/pod-a.txt && echo 'YES' || echo 'NO')"
        echo "Volume mount info:"
        mount | grep /data
        echo "================================"
        sleep 30
      done
    volumeMounts:
    - name: shared-volume
      mountPath: /data
  volumes:
  - name: shared-volume
    persistentVolumeClaim:
      claimName: shared-volume

---
# Job to verify controller functionality
apiVersion: batch/v1
kind: Job
metadata:
  name: controller-verification
  namespace: controller-test-a
spec:
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: verifier
        image: busybox:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "=== Controller Server Verification ==="
          echo "This job verifies the controller server functionality:"
          echo ""
          echo "1. CreateVolume generates consistent volume IDs for cross-namespace sharing"
          echo "2. ValidateVolumeCapabilities supports ReadWriteMany access mode"
          echo "3. Volume context includes cross-namespace sharing metadata"
          echo ""
          echo "Expected behavior:"
          echo "- Both PVCs should get the same volume ID (cross-ns-shared-volume-XXXX)"
          echo "- Volume context should include 'xpai-csi/cross-namespace-sharing=true'"
          echo "- Volume context should include 'xpai-csi/pvc-name=shared-volume'"
          echo ""
          echo "Verification commands:"
          echo "# Check PV details for both PVCs"
          echo "kubectl get pvc shared-volume -n controller-test-a -o yaml"
          echo "kubectl get pvc shared-volume -n controller-test-b -o yaml"
          echo ""
          echo "# Check if volume IDs are the same"
          echo "kubectl get pv -o jsonpath='{.items[*].spec.csi.volumeHandle}' | tr ' ' '\n' | grep cross-ns"
          echo ""
          echo "# Check volume context"
          echo "kubectl get pv -o jsonpath='{.items[*].spec.csi.volumeAttributes}'"
          echo ""
          echo "# Check controller logs"
          echo "kubectl logs -n kube-system -l app=csi-controller-xpai | grep -i 'cross-namespace\\|CreateVolume'"
          echo ""
          echo "Controller verification completed at $(date)"

---
# Test different access modes
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: test-rwo
  namespace: controller-test-a
spec:
  accessModes:
    - ReadWriteOnce  # Test single-node access mode
  resources:
    requests:
      storage: 1Gi
  storageClassName: controller-test-storage

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: test-rox
  namespace: controller-test-a
spec:
  accessModes:
    - ReadOnlyMany  # Test read-only access mode
  resources:
    requests:
      storage: 1Gi
  storageClassName: controller-test-storage
