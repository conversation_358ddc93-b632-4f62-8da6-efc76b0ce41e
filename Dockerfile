FROM alpine:latest
RUN apk add --update curl tzdata iproute2 bash libc6-compat vim &&  \
 	rm -rf /var/cache/apk/* && \
 	cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
 	echo "Asia/Shanghai" >  /etc/timezone
ARG TARGETARCH
ENV PATH=/opt/bin:$PATH
COPY xpai-csi-${TARGETARCH} /opt/bin/xpai-csi
COPY entrypoint.sh /opt/bin/entrypoint.sh
COPY check_bind_mounts.sh /opt/bin/check_bind_mounts.sh
COPY check_mount.sh /opt/bin/check_mount.sh
RUN chmod u+x /opt/bin/xpai-csi && \
    chmod u+x /opt/bin/entrypoint.sh && \
    chmod u+x /opt/bin/check_bind_mounts.sh && \
    chmod u+x /opt/bin/check_mount.sh

ENTRYPOINT ["entrypoint.sh"]
