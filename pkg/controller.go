package pkg

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"io"
	"strings"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"github.com/kubegems/xpai-csi/pkg/filesystem"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ csi.ControllerServer = &controllerServer{}

type controllerServer struct {
	client     client.Client
	kubeClient kubernetes.Interface
	fsManager  *filesystem.FilesystemManager
	csi.UnimplementedControllerServer
}

var csc = []*csi.ControllerServiceCapability{
	{
		Type: &csi.ControllerServiceCapability_Rpc{
			Rpc: &csi.ControllerServiceCapability_RPC{
				Type: csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME,
			},
		},
	},
}

func newControllerServer(client client.Client, kubeClient kubernetes.Interface, fsManager *filesystem.FilesystemManager) csi.ControllerServer {
	return &controllerServer{
		client:     client,
		kubeClient: kubeClient,
		fsManager:  fsManager,
	}
}

// ControllerExpandVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerExpandVolume(ctx context.Context, req *csi.ControllerExpandVolumeRequest) (*csi.ControllerExpandVolumeResponse, error) {
	glog.V(5).Infof("ControllerExpandVolume called with request: %s", req.String())
	return &csi.ControllerExpandVolumeResponse{}, status.Error(codes.Unimplemented, "ControllerExpandVolume is not implemented")

}

// ControllerGetCapabilities implements csi.ControllerServer.
func (c *controllerServer) ControllerGetCapabilities(ctx context.Context, req *csi.ControllerGetCapabilitiesRequest) (*csi.ControllerGetCapabilitiesResponse, error) {
	glog.V(5).Infof("ControllerGetCapabilities called with request: %s", req.String())
	return &csi.ControllerGetCapabilitiesResponse{
		Capabilities: csc,
	}, nil
}

func (c *controllerServer) validateControllerServiceRequest(rpc csi.ControllerServiceCapability_RPC_Type) error {
	if rpc == csi.ControllerServiceCapability_RPC_UNKNOWN {
		return nil
	}

	for _, cap := range csc {
		if rpc == cap.GetRpc().GetType() {
			return nil
		}
	}
	return status.Error(codes.InvalidArgument, rpc.String())
}

// ControllerGetVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerGetVolume(ctx context.Context, req *csi.ControllerGetVolumeRequest) (*csi.ControllerGetVolumeResponse, error) {
	glog.V(5).Infof("ControllerGetVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerGetVolume is not implemented")
}

// ControllerModifyVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerModifyVolume(ctx context.Context, req *csi.ControllerModifyVolumeRequest) (*csi.ControllerModifyVolumeResponse, error) {
	glog.V(5).Infof("ControllerModifyVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerModifyVolume is not implemented")
}

// ControllerPublishVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerPublishVolume(ctx context.Context, req *csi.ControllerPublishVolumeRequest) (*csi.ControllerPublishVolumeResponse, error) {
	glog.V(5).Infof("ControllerPublishVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerPublishVolume is not implemented")
}

// ControllerUnpublishVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerUnpublishVolume(ctx context.Context, req *csi.ControllerUnpublishVolumeRequest) (*csi.ControllerUnpublishVolumeResponse, error) {
	glog.V(5).Infof("ControllerUnpublishVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerUnpublishVolume is not implemented")
}

// CreateSnapshot implements csi.ControllerServer.
func (c *controllerServer) CreateSnapshot(ctx context.Context, req *csi.CreateSnapshotRequest) (*csi.CreateSnapshotResponse, error) {
	glog.V(5).Infof("CreateSnapshot called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "CreateSnapshot is not implemented")
}

// CreateVolume implements csi.ControllerServer.
func (c *controllerServer) CreateVolume(ctx context.Context, request *csi.CreateVolumeRequest) (*csi.CreateVolumeResponse, error) {
	glog.V(5).Infof("CreateVolume called with request: %s", request.String())

	// Validate controller service capability
	if err := c.validateControllerServiceRequest(csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME); err != nil {
		glog.V(5).Infof("Invalid create volume req: %v", err)
		return nil, err
	}

	// Validate request parameters
	volumeName := request.GetName()
	if len(volumeName) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Name missing in request")
	}

	if request.GetVolumeCapabilities() == nil {
		return nil, status.Error(codes.InvalidArgument, "Volume Capabilities missing in request")
	}

	// Validate volume capabilities
	for _, capability := range request.GetVolumeCapabilities() {
		if capability.GetAccessMode() == nil {
			return nil, status.Error(codes.InvalidArgument, "Access mode missing in volume capability")
		}

		// Check if access mode is supported
		accessMode := capability.GetAccessMode().GetMode()
		if accessMode != csi.VolumeCapability_AccessMode_SINGLE_NODE_WRITER &&
			accessMode != csi.VolumeCapability_AccessMode_MULTI_NODE_MULTI_WRITER &&
			accessMode != csi.VolumeCapability_AccessMode_MULTI_NODE_READER_ONLY {
			return nil, status.Errorf(codes.InvalidArgument, "Unsupported access mode: %v", accessMode)
		}
	}

	// Get parameters
	parameters := request.GetParameters()
	if parameters == nil {
		parameters = make(map[string]string)
	}

	// Parse filesystem type
	fsType := parameters["type"]
	if fsType == "" {
		return nil, status.Error(codes.InvalidArgument, "CreateVolume operation requires 'type' parameter")
	}

	// Generate volume ID based on cross-namespace sharing configuration
	volumeID := c.generateVolumeID(volumeName, parameters)

	// Check if cross-namespace sharing is enabled
	enableCrossNS := parameters["enableCrossNamespaceSharing"] == "true"

	// Create volume context
	volumeContext := make(map[string]string)
	for k, v := range parameters {
		volumeContext[k] = v
	}

	// Add metadata for cross-namespace sharing
	if enableCrossNS {
		volumeContext["xpai-csi/cross-namespace-sharing"] = "true"
		volumeContext["xpai-csi/pvc-name"] = volumeName
		glog.V(2).Infof("Created volume %s with cross-namespace sharing enabled for PVC name: %s", volumeID, volumeName)
	}

	// Get required capacity
	capacityBytes := int64(request.GetCapacityRange().GetRequiredBytes())

	glog.V(2).Infof("Successfully created volume %s (type: %s, size: %d bytes, cross-ns: %v)", volumeID, fsType, capacityBytes, enableCrossNS)

	return &csi.CreateVolumeResponse{
		Volume: &csi.Volume{
			VolumeId:      volumeID,
			CapacityBytes: capacityBytes,
			VolumeContext: volumeContext,
		},
	}, nil
}

// DeleteSnapshot implements csi.ControllerServer.
func (c *controllerServer) DeleteSnapshot(ctx context.Context, req *csi.DeleteSnapshotRequest) (*csi.DeleteSnapshotResponse, error) {
	glog.V(5).Infof("DeleteSnapshot called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "DeleteSnapshot is not implemented")
}

// DeleteVolume implements csi.ControllerServer.
func (c *controllerServer) DeleteVolume(ctx context.Context, req *csi.DeleteVolumeRequest) (*csi.DeleteVolumeResponse, error) {
	glog.V(5).Infof("DeleteVolume called with request: %s", req.String())
	volumeID := req.GetVolumeId()

	// Check arguments
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Volume ID missing in request")
	}

	if err := c.validateControllerServiceRequest(csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME); err != nil {
		glog.Infof("Invalid delete volume req: %v", err)
		return nil, err
	}
	glog.V(5).Infof("Deleting volume %s", volumeID)

	return &csi.DeleteVolumeResponse{}, nil
}

// GetCapacity implements csi.ControllerServer.
func (c *controllerServer) GetCapacity(ctx context.Context, req *csi.GetCapacityRequest) (*csi.GetCapacityResponse, error) {
	glog.V(5).Infof("GetCapacity called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "GetCapacity is not implemented")
}

// ListSnapshots implements csi.ControllerServer.
func (c *controllerServer) ListSnapshots(ctx context.Context, req *csi.ListSnapshotsRequest) (*csi.ListSnapshotsResponse, error) {
	glog.V(5).Infof("ListSnapshots called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ListSnapshots is not implemented")
}

// ListVolumes implements csi.ControllerServer.
func (c *controllerServer) ListVolumes(ctx context.Context, req *csi.ListVolumesRequest) (*csi.ListVolumesResponse, error) {
	glog.V(5).Infof("ListVolumes called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ListVolumes is not implemented")
}

// ValidateVolumeCapabilities implements csi.ControllerServer.
func (c *controllerServer) ValidateVolumeCapabilities(ctx context.Context, req *csi.ValidateVolumeCapabilitiesRequest) (*csi.ValidateVolumeCapabilitiesResponse, error) {
	glog.V(5).Infof("ValidateVolumeCapabilities called with request: %s", req.String())
	// Check arguments
	if len(req.GetVolumeId()) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Volume ID missing in request")
	}
	if req.GetVolumeCapabilities() == nil {
		return nil, status.Error(codes.InvalidArgument, "Volume capabilities missing in request")
	}

	// Supported access modes
	supportedAccessModes := []*csi.VolumeCapability_AccessMode{
		{Mode: csi.VolumeCapability_AccessMode_SINGLE_NODE_WRITER},
		{Mode: csi.VolumeCapability_AccessMode_MULTI_NODE_MULTI_WRITER},
		{Mode: csi.VolumeCapability_AccessMode_MULTI_NODE_READER_ONLY},
	}

	// Validate each requested capability
	confirmedCapabilities := []*csi.VolumeCapability{}
	for _, requestedCap := range req.VolumeCapabilities {
		requestedMode := requestedCap.GetAccessMode().GetMode()

		// Check if the requested access mode is supported
		supported := false
		for _, supportedMode := range supportedAccessModes {
			if requestedMode == supportedMode.GetMode() {
				supported = true
				confirmedCapabilities = append(confirmedCapabilities, &csi.VolumeCapability{
					AccessMode: supportedMode,
				})
				break
			}
		}

		if !supported {
			return &csi.ValidateVolumeCapabilitiesResponse{
				Message: fmt.Sprintf("Unsupported access mode: %v", requestedMode),
			}, nil
		}
	}

	return &csi.ValidateVolumeCapabilitiesResponse{
		Confirmed: &csi.ValidateVolumeCapabilitiesResponse_Confirmed{
			VolumeCapabilities: confirmedCapabilities,
		},
	}, nil
}

func sanitizeVolumeID(volumeID string) string {
	volumeID = strings.ToLower(volumeID)
	if len(volumeID) > 63 {
		h := sha1.New()
		_, err := io.WriteString(h, volumeID)
		if err != nil {
			glog.Warningf("writeString error %v", err)
		}
		volumeID = hex.EncodeToString(h.Sum(nil))
	}
	return volumeID
}

// generateVolumeID generates a volume ID based on volume name and parameters
func (c *controllerServer) generateVolumeID(volumeName string, parameters map[string]string) string {
	// Check if cross-namespace sharing is enabled
	enableCrossNS := parameters["enableCrossNamespaceSharing"] == "true"

	if enableCrossNS {
		// For cross-namespace sharing, generate ID based on PVC name and StorageClass parameters
		// This ensures that PVCs with the same name from the same StorageClass get the same volume ID
		h := sha1.New()
		h.Write([]byte(volumeName))

		// Add key parameters that define the storage configuration
		if fsType := parameters["type"]; fsType != "" {
			h.Write([]byte(fsType))
		}
		if server := parameters["server"]; server != "" {
			h.Write([]byte(server))
		}
		if path := parameters["path"]; path != "" {
			h.Write([]byte(path))
		}
		if nfsVersion := parameters["nfsVersion"]; nfsVersion != "" {
			h.Write([]byte(nfsVersion))
		}
		if mountFlags := parameters["mountFlags"]; mountFlags != "" {
			h.Write([]byte(mountFlags))
		}

		// Add other filesystem-specific parameters
		for key, value := range parameters {
			if key != "enableCrossNamespaceSharing" && key != "type" &&
				key != "server" && key != "path" && key != "nfsVersion" && key != "mountFlags" {
				h.Write([]byte(key + "=" + value))
			}
		}

		return fmt.Sprintf("cross-ns-%s-%s", volumeName, hex.EncodeToString(h.Sum(nil))[:16])
	} else {
		// For regular volumes, use the original sanitized approach
		return sanitizeVolumeID(volumeName)
	}
}
