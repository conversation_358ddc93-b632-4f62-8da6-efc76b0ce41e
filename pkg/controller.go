package pkg

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"io"
	"strings"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var _ csi.ControllerServer = &controllerServer{}

type controllerServer struct {
	//Name, Version string
	csi.UnimplementedControllerServer
}

var csc = []*csi.ControllerServiceCapability{
	{
		Type: &csi.ControllerServiceCapability_Rpc{
			Rpc: &csi.ControllerServiceCapability_RPC{
				Type: csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME,
			},
		},
	},
}

func newControllerServer() csi.ControllerServer {
	return &controllerServer{}
}

// ControllerExpandVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerExpandVolume(ctx context.Context, req *csi.ControllerExpandVolumeRequest) (*csi.ControllerExpandVolumeResponse, error) {
	glog.V(5).Infof("ControllerExpandVolume called with request: %s", req.String())
	return &csi.ControllerExpandVolumeResponse{}, status.Error(codes.Unimplemented, "ControllerExpandVolume is not implemented")

}

// ControllerGetCapabilities implements csi.ControllerServer.
func (c *controllerServer) ControllerGetCapabilities(ctx context.Context, req *csi.ControllerGetCapabilitiesRequest) (*csi.ControllerGetCapabilitiesResponse, error) {
	glog.V(5).Infof("ControllerGetCapabilities called with request: %s", req.String())
	return &csi.ControllerGetCapabilitiesResponse{
		Capabilities: csc,
	}, nil
}

func (c *controllerServer) validateControllerServiceRequest(rpc csi.ControllerServiceCapability_RPC_Type) error {
	if rpc == csi.ControllerServiceCapability_RPC_UNKNOWN {
		return nil
	}

	for _, cap := range csc {
		if rpc == cap.GetRpc().GetType() {
			return nil
		}
	}
	return status.Error(codes.InvalidArgument, rpc.String())
}

// ControllerGetVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerGetVolume(ctx context.Context, req *csi.ControllerGetVolumeRequest) (*csi.ControllerGetVolumeResponse, error) {
	glog.V(5).Infof("ControllerGetVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerGetVolume is not implemented")
}

// ControllerModifyVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerModifyVolume(ctx context.Context, req *csi.ControllerModifyVolumeRequest) (*csi.ControllerModifyVolumeResponse, error) {
	glog.V(5).Infof("ControllerModifyVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerModifyVolume is not implemented")
}

// ControllerPublishVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerPublishVolume(ctx context.Context, req *csi.ControllerPublishVolumeRequest) (*csi.ControllerPublishVolumeResponse, error) {
	glog.V(5).Infof("ControllerPublishVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerPublishVolume is not implemented")
}

// ControllerUnpublishVolume implements csi.ControllerServer.
func (c *controllerServer) ControllerUnpublishVolume(ctx context.Context, req *csi.ControllerUnpublishVolumeRequest) (*csi.ControllerUnpublishVolumeResponse, error) {
	glog.V(5).Infof("ControllerUnpublishVolume called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ControllerUnpublishVolume is not implemented")
}

// CreateSnapshot implements csi.ControllerServer.
func (c *controllerServer) CreateSnapshot(ctx context.Context, req *csi.CreateSnapshotRequest) (*csi.CreateSnapshotResponse, error) {
	glog.V(5).Infof("CreateSnapshot called volume: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "CreateSnapshot is not implemented")
}

// CreateVolume implements csi.ControllerServer.
func (c *controllerServer) CreateVolume(ctx context.Context, request *csi.CreateVolumeRequest) (*csi.CreateVolumeResponse, error) {
	glog.V(5).Infof("CreateVolume called with request: %s", request.String())
	volumeID := sanitizeVolumeID(request.GetName())

	if err := c.validateControllerServiceRequest(csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME); err != nil {
		glog.V(5).Infof("Invalid create volume req: %v", err)
		return nil, err
	}
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Name missing in request")
	}
	if request.GetVolumeCapabilities() == nil {
		return nil, status.Error(codes.InvalidArgument, "Volume Capabilities missing in request")
	}
	capacityBytes := int64(request.GetCapacityRange().GetRequiredBytes())

	glog.V(5).Infof("Creating volume %s", volumeID)
	return &csi.CreateVolumeResponse{
		Volume: &csi.Volume{
			VolumeId:      volumeID,
			CapacityBytes: capacityBytes,
			VolumeContext: request.GetParameters(),
		},
	}, nil
}

// DeleteSnapshot implements csi.ControllerServer.
func (c *controllerServer) DeleteSnapshot(ctx context.Context, req *csi.DeleteSnapshotRequest) (*csi.DeleteSnapshotResponse, error) {
	glog.V(5).Infof("DeleteSnapshot called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "DeleteSnapshot is not implemented")
}

// DeleteVolume implements csi.ControllerServer.
func (c *controllerServer) DeleteVolume(ctx context.Context, req *csi.DeleteVolumeRequest) (*csi.DeleteVolumeResponse, error) {
	glog.V(5).Infof("DeleteVolume called with request: %s", req.String())
	volumeID := req.GetVolumeId()

	// Check arguments
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Volume ID missing in request")
	}

	if err := c.validateControllerServiceRequest(csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME); err != nil {
		glog.Infof("Invalid delete volume req: %v", err)
		return nil, err
	}
	glog.V(5).Infof("Deleting volume %s", volumeID)

	return &csi.DeleteVolumeResponse{}, nil
}

// GetCapacity implements csi.ControllerServer.
func (c *controllerServer) GetCapacity(ctx context.Context, req *csi.GetCapacityRequest) (*csi.GetCapacityResponse, error) {
	glog.V(5).Infof("GetCapacity called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "GetCapacity is not implemented")
}

// ListSnapshots implements csi.ControllerServer.
func (c *controllerServer) ListSnapshots(ctx context.Context, req *csi.ListSnapshotsRequest) (*csi.ListSnapshotsResponse, error) {
	glog.V(5).Infof("ListSnapshots called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ListSnapshots is not implemented")
}

// ListVolumes implements csi.ControllerServer.
func (c *controllerServer) ListVolumes(ctx context.Context, req *csi.ListVolumesRequest) (*csi.ListVolumesResponse, error) {
	glog.V(5).Infof("ListVolumes called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "ListVolumes is not implemented")
}

// ValidateVolumeCapabilities implements csi.ControllerServer.
func (c *controllerServer) ValidateVolumeCapabilities(ctx context.Context, req *csi.ValidateVolumeCapabilitiesRequest) (*csi.ValidateVolumeCapabilitiesResponse, error) {
	glog.V(5).Infof("ValidateVolumeCapabilities called with request: %s", req.String())
	// Check arguments
	if len(req.GetVolumeId()) == 0 {
		return nil, status.Error(codes.InvalidArgument, "Volume ID missing in request")
	}
	if req.GetVolumeCapabilities() == nil {
		return nil, status.Error(codes.InvalidArgument, "Volume capabilities missing in request")
	}

	// We currently only support RWO
	supportedAccessMode := &csi.VolumeCapability_AccessMode{
		Mode: csi.VolumeCapability_AccessMode_SINGLE_NODE_WRITER,
	}

	for _, cap := range req.VolumeCapabilities {
		if cap.GetAccessMode().GetMode() != supportedAccessMode.GetMode() {
			return &csi.ValidateVolumeCapabilitiesResponse{Message: "Only single node writer is supported"}, nil
		}
	}

	return &csi.ValidateVolumeCapabilitiesResponse{
		Confirmed: &csi.ValidateVolumeCapabilitiesResponse_Confirmed{
			VolumeCapabilities: []*csi.VolumeCapability{
				{
					AccessMode: supportedAccessMode,
				},
			},
		},
	}, nil
}

func sanitizeVolumeID(volumeID string) string {
	volumeID = strings.ToLower(volumeID)
	if len(volumeID) > 63 {
		h := sha1.New()
		_, err := io.WriteString(h, volumeID)
		if err != nil {
			glog.Warningf("writeString error %v", err)
		}
		volumeID = hex.EncodeToString(h.Sum(nil))
	}
	return volumeID
}
