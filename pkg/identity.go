package pkg

import (
	"context"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var _ csi.IdentityServer = &identityServer{}

type identityServer struct {
	Name, Version string
	csi.UnimplementedIdentityServer
}

func newIdentityServer(name, version string) csi.IdentityServer {
	return &identityServer{
		Name:    name,
		Version: version,
	}
}

// GetPluginCapabilities implements csi.IdentityServer.
func (ids *identityServer) GetPluginCapabilities(ctx context.Context, req *csi.GetPluginCapabilitiesRequest) (*csi.GetPluginCapabilitiesResponse, error) {
	glog.V(5).Infof("GetPluginCapabilities called with request: %s", req.String())
	return &csi.GetPluginCapabilitiesResponse{
		Capabilities: []*csi.PluginCapability{
			{
				Type: &csi.PluginCapability_Service_{
					Service: &csi.PluginCapability_Service{
						Type: csi.PluginCapability_Service_CONTROLLER_SERVICE,
					},
				},
			},
		},
	}, nil
}

// GetPluginInfo implements csi.IdentityServer.
func (ids *identityServer) GetPluginInfo(ctx context.Context, req *csi.GetPluginInfoRequest) (*csi.GetPluginInfoResponse, error) {
	glog.V(5).Infof("GetPluginInfo called with request: %s", req.String())
	if ids.Name == "" {
		return nil, status.Error(codes.Unavailable, "Driver name not configured")
	}
	if ids.Version == "" {
		return nil, status.Error(codes.Unavailable, "Driver is missing version")
	}
	return &csi.GetPluginInfoResponse{
		Name:          ids.Name,
		VendorVersion: ids.Version,
	}, nil
}

// Probe implements csi.IdentityServer.
func (ids *identityServer) Probe(ctx context.Context, req *csi.ProbeRequest) (*csi.ProbeResponse, error) {
	glog.V(5).Infof("Probe called with request: %s", req.String())
	return &csi.ProbeResponse{}, nil
}
