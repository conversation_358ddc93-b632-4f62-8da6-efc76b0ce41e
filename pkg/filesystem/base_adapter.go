package filesystem

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/golang/glog"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// BaseAdapter provides common functionality for filesystem adapters
type BaseAdapter struct {
	client     client.Client
	kubeClient kubernetes.Interface
	nodeID     string
	fsType     FilesystemType
}

// NewBaseAdapter creates a new base adapter
func NewBaseAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string, fsType FilesystemType) *BaseAdapter {
	return &BaseAdapter{
		client:     client,
		kubeClient: kubeClient,
		nodeID:     nodeID,
		fsType:     fsType,
	}
}

// GetType returns the filesystem type
func (b *BaseAdapter) GetType() FilesystemType {
	return b.fsType
}

// CreateMountPod creates a mount pod based on the provided spec
func (b *BaseAdapter) CreateMountPod(ctx context.Context, spec *MountPodSpec) error {
	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      spec.Name,
			Namespace: spec.Namespace,
			Labels: map[string]string{
				"app":                    "xpai-csi-mount",
				"xpai-csi/filesystem":    string(b.fsType),
				"xpai-csi/node":          b.nodeID,
				"xpai-csi/mount-pod":     "true",
			},
		},
		Spec: v1.PodSpec{
			NodeName:      b.nodeID,
			RestartPolicy: v1.RestartPolicyNever,
			Containers: []v1.Container{
				{
					Name:            "mount",
					Image:           spec.Image,
					Command:         spec.Command,
					Args:            spec.Args,
					Env:             spec.Env,
					VolumeMounts:    spec.VolumeMounts,
					Resources:       spec.Resources,
					SecurityContext: spec.SecurityContext,
				},
			},
			Volumes:       spec.Volumes,
			NodeSelector:  spec.NodeSelector,
			Tolerations:   spec.Tolerations,
			HostNetwork:   true,
			HostPID:       true,
		},
	}
	
	// Create the pod
	_, err := b.kubeClient.CoreV1().Pods(spec.Namespace).Create(ctx, pod, metav1.CreateOptions{})
	if err != nil {
		return fmt.Errorf("failed to create mount pod: %v", err)
	}
	
	glog.V(2).Infof("Created mount pod %s/%s", spec.Namespace, spec.Name)
	return nil
}

// WaitForMountPod waits for the mount pod to complete successfully
func (b *BaseAdapter) WaitForMountPod(ctx context.Context, namespace, name string, timeout time.Duration) error {
	return wait.PollImmediate(2*time.Second, timeout, func() (bool, error) {
		pod, err := b.kubeClient.CoreV1().Pods(namespace).Get(ctx, name, metav1.GetOptions{})
		if err != nil {
			return false, err
		}
		
		switch pod.Status.Phase {
		case v1.PodSucceeded:
			glog.V(2).Infof("Mount pod %s/%s completed successfully", namespace, name)
			return true, nil
		case v1.PodFailed:
			return false, fmt.Errorf("mount pod %s/%s failed: %s", namespace, name, pod.Status.Message)
		case v1.PodPending, v1.PodRunning:
			glog.V(4).Infof("Mount pod %s/%s is still %s", namespace, name, pod.Status.Phase)
			return false, nil
		default:
			return false, fmt.Errorf("mount pod %s/%s in unexpected phase: %s", namespace, name, pod.Status.Phase)
		}
	})
}

// DeleteMountPod deletes a mount pod
func (b *BaseAdapter) DeleteMountPod(ctx context.Context, namespace, name string) error {
	err := b.kubeClient.CoreV1().Pods(namespace).Delete(ctx, name, metav1.DeleteOptions{})
	if err != nil {
		return fmt.Errorf("failed to delete mount pod: %v", err)
	}
	
	glog.V(2).Infof("Deleted mount pod %s/%s", namespace, name)
	return nil
}

// EnsureDirectory ensures that a directory exists
func (b *BaseAdapter) EnsureDirectory(path string) error {
	if err := os.MkdirAll(path, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %v", path, err)
	}
	return nil
}

// IsMountPoint checks if a path is a mount point
func (b *BaseAdapter) IsMountPoint(path string) (bool, error) {
	// Read /proc/self/mountinfo to check if path is mounted
	// This is a simplified implementation
	// In production, you might want to use a more robust method
	
	// Check if the path exists
	if _, err := os.Stat(path); err != nil {
		return false, err
	}
	
	// For now, we'll use a simple check
	// TODO: Implement proper mount point detection
	return true, nil
}

// GeneratePodName generates a unique pod name for mount operations
func (b *BaseAdapter) GeneratePodName(volumeID string) string {
	// Clean the volume ID to make it suitable for pod name
	cleanID := strings.ReplaceAll(volumeID, "_", "-")
	cleanID = strings.ToLower(cleanID)
	
	// Truncate if too long (pod names have a 63 character limit)
	if len(cleanID) > 40 {
		cleanID = cleanID[:40]
	}
	
	return fmt.Sprintf("xpai-mount-%s-%s", string(b.fsType), cleanID)
}

// GetMountNamespace returns the namespace for mount pods
func (b *BaseAdapter) GetMountNamespace() string {
	// Use kube-system namespace for mount pods
	return "kube-system"
}

// GetDefaultMountImage returns the default image for mount operations
func (b *BaseAdapter) GetDefaultMountImage() string {
	// This should be configurable in production
	return "registry.cn-beijing.aliyuncs.com/kubegems/xpai-csi:v0.0.1"
}

// ParseCommonParameters parses common parameters from storage class
func (b *BaseAdapter) ParseCommonParameters(parameters map[string]string) (*MountOptions, error) {
	options := &MountOptions{
		ExtraOptions: make(map[string]string),
	}
	
	// Parse read-only flag
	if ro, exists := parameters["readOnly"]; exists {
		options.ReadOnly = strings.ToLower(ro) == "true"
	}
	
	// Parse mount flags
	if flags, exists := parameters["mountFlags"]; exists {
		options.MountFlags = strings.Split(flags, ",")
	}
	
	// Parse sub path
	if subPath, exists := parameters["subPath"]; exists {
		options.SubPath = subPath
	}
	
	// Parse path
	if path, exists := parameters["path"]; exists {
		options.Path = path
	}
	
	// Copy extra options
	for key, value := range parameters {
		if !isCommonParameter(key) {
			options.ExtraOptions[key] = value
		}
	}
	
	return options, nil
}

// isCommonParameter checks if a parameter is a common parameter
func isCommonParameter(key string) bool {
	commonParams := []string{
		"type", "readOnly", "mountFlags", "subPath", "path",
	}
	
	for _, param := range commonParams {
		if key == param {
			return true
		}
	}
	return false
}

// ValidateCommonParameters validates common parameters
func (b *BaseAdapter) ValidateCommonParameters(parameters map[string]string) error {
	// Validate that required 'type' parameter exists
	if _, exists := parameters["type"]; !exists {
		return fmt.Errorf("missing required parameter 'type'")
	}
	
	// Validate read-only parameter if present
	if ro, exists := parameters["readOnly"]; exists {
		if ro != "true" && ro != "false" {
			return fmt.Errorf("readOnly parameter must be 'true' or 'false', got: %s", ro)
		}
	}
	
	return nil
}

// GetVolumeStatsPath returns the path for volume statistics
func (b *BaseAdapter) GetVolumeStatsPath(mountPath string) string {
	return filepath.Join(mountPath, ".stats")
}
