package filesystem

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/golang/glog"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// MountInfo contains information about a mounted volume
type MountInfo struct {
	VolumeID       string            `json:"volumeId"`
	MountPath      string            `json:"mountPath"`
	FilesystemType string            `json:"filesystemType"`
	MountOptions   map[string]string `json:"mountOptions"`
	LastCheck      time.Time         `json:"lastCheck"`
	Healthy        bool              `json:"healthy"`
	ErrorCount     int               `json:"errorCount"`
	LastError      string            `json:"lastError,omitempty"`
}

// HealthChecker manages health checking for mounted volumes
type HealthChecker struct {
	client        client.Client
	kubeClient    kubernetes.Interface
	nodeID        string
	fsManager     *FilesystemManager
	mountRegistry map[string]*MountInfo
	mutex         sync.RWMutex
	stopCh        chan struct{}
	checkInterval time.Duration
	maxRetries    int
}

// HealthCheckerConfig contains configuration for HealthChecker
type HealthCheckerConfig struct {
	Client        client.Client
	KubeClient    kubernetes.Interface
	NodeID        string
	FSManager     *FilesystemManager
	CheckInterval time.Duration
	MaxRetries    int
}

// NewHealthChecker creates a new health checker
func NewHealthChecker(config *HealthCheckerConfig) *HealthChecker {
	if config.CheckInterval == 0 {
		config.CheckInterval = 30 * time.Second // Default check interval
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3 // Default max retries
	}

	return &HealthChecker{
		client:        config.Client,
		kubeClient:    config.KubeClient,
		nodeID:        config.NodeID,
		fsManager:     config.FSManager,
		mountRegistry: make(map[string]*MountInfo),
		stopCh:        make(chan struct{}),
		checkInterval: config.CheckInterval,
		maxRetries:    config.MaxRetries,
	}
}

// Start starts the health checker
func (hc *HealthChecker) Start(ctx context.Context) error {
	glog.V(2).Infof("Starting filesystem health checker with interval %v", hc.checkInterval)

	ticker := time.NewTicker(hc.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			glog.V(2).Info("Health checker stopped due to context cancellation")
			return nil
		case <-hc.stopCh:
			glog.V(2).Info("Health checker stopped")
			return nil
		case <-ticker.C:
			hc.performHealthChecks(ctx)
		}
	}
}

// Stop stops the health checker
func (hc *HealthChecker) Stop() {
	close(hc.stopCh)
}

// RegisterMount registers a mount for health checking
func (hc *HealthChecker) RegisterMount(volumeID, mountPath, fsType string, options map[string]string) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	hc.mountRegistry[volumeID] = &MountInfo{
		VolumeID:       volumeID,
		MountPath:      mountPath,
		FilesystemType: fsType,
		MountOptions:   options,
		LastCheck:      time.Now(),
		Healthy:        true,
		ErrorCount:     0,
	}

	glog.V(2).Infof("Registered mount for health checking: volume=%s, path=%s, type=%s", volumeID, mountPath, fsType)
}

// UnregisterMount unregisters a mount from health checking
func (hc *HealthChecker) UnregisterMount(volumeID string) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	delete(hc.mountRegistry, volumeID)
	glog.V(2).Infof("Unregistered mount from health checking: volume=%s", volumeID)
}

// GetMountHealth returns the health status of a mount
func (hc *HealthChecker) GetMountHealth(volumeID string) (*MountInfo, bool) {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	info, exists := hc.mountRegistry[volumeID]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	infoCopy := *info
	return &infoCopy, true
}

// GetAllMountHealth returns health status of all mounts
func (hc *HealthChecker) GetAllMountHealth() map[string]*MountInfo {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	result := make(map[string]*MountInfo)
	for volumeID, info := range hc.mountRegistry {
		infoCopy := *info
		result[volumeID] = &infoCopy
	}

	return result
}

// performHealthChecks performs health checks on all registered mounts
func (hc *HealthChecker) performHealthChecks(ctx context.Context) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	for volumeID, mountInfo := range hc.mountRegistry {
		hc.checkMountHealth(ctx, volumeID, mountInfo)
	}
}

// checkMountHealth checks the health of a specific mount
func (hc *HealthChecker) checkMountHealth(ctx context.Context, volumeID string, mountInfo *MountInfo) {
	glog.V(4).Infof("Checking health for volume %s at path %s", volumeID, mountInfo.MountPath)

	mountInfo.LastCheck = time.Now()

	// Get the appropriate filesystem adapter
	adapter, err := hc.fsManager.GetAdapter(FilesystemType(mountInfo.FilesystemType))
	if err != nil {
		hc.recordHealthCheckError(mountInfo, fmt.Sprintf("Failed to get adapter: %v", err))
		return
	}

	// Perform health check
	if err := adapter.HealthCheck(ctx, mountInfo.MountPath); err != nil {
		hc.recordHealthCheckError(mountInfo, fmt.Sprintf("Health check failed: %v", err))

		// If error count exceeds threshold, attempt recovery
		if mountInfo.ErrorCount >= hc.maxRetries {
			glog.Warningf("Mount %s has failed %d times, attempting recovery", volumeID, mountInfo.ErrorCount)
			hc.attemptMountRecovery(ctx, volumeID, mountInfo, adapter)
		}
	} else {
		// Health check passed
		if !mountInfo.Healthy {
			glog.V(2).Infof("Mount %s recovered and is now healthy", volumeID)
		}
		mountInfo.Healthy = true
		mountInfo.ErrorCount = 0
		mountInfo.LastError = ""
	}
}

// recordHealthCheckError records a health check error
func (hc *HealthChecker) recordHealthCheckError(mountInfo *MountInfo, errorMsg string) {
	mountInfo.Healthy = false
	mountInfo.ErrorCount++
	mountInfo.LastError = errorMsg

	glog.Warningf("Health check failed for mount %s (error count: %d): %s",
		mountInfo.VolumeID, mountInfo.ErrorCount, errorMsg)
}

// attemptMountRecovery attempts to recover a failed mount
func (hc *HealthChecker) attemptMountRecovery(ctx context.Context, volumeID string, mountInfo *MountInfo, adapter FilesystemAdapter) {
	glog.V(2).Infof("Attempting recovery for mount %s at path %s", volumeID, mountInfo.MountPath)

	// Create a volume context for recovery
	volumeCtx := &VolumeContext{
		VolumeID:   volumeID,
		VolumeName: volumeID,
		Parameters: map[string]string{
			"type": mountInfo.FilesystemType,
		},
		MountOptions: &MountOptions{
			ExtraOptions: mountInfo.MountOptions,
		},
		Attributes: make(map[string]string),
	}

	// Try to unmount first
	if err := adapter.Unmount(ctx, volumeCtx, mountInfo.MountPath); err != nil {
		glog.Warningf("Failed to unmount during recovery for volume %s: %v", volumeID, err)
	}

	// Wait a bit before remounting
	time.Sleep(5 * time.Second)

	// Try to remount
	result, err := adapter.Mount(ctx, volumeCtx, mountInfo.MountPath, mountInfo.MountPath)
	if err != nil {
		glog.Errorf("Failed to remount during recovery for volume %s: %v", volumeID, err)
		mountInfo.LastError = fmt.Sprintf("Recovery failed: %v", err)
		return
	}

	if result.Success {
		glog.V(2).Infof("Successfully recovered mount for volume %s", volumeID)
		mountInfo.Healthy = true
		mountInfo.ErrorCount = 0
		mountInfo.LastError = ""
	} else {
		glog.Errorf("Mount recovery failed for volume %s: %v", volumeID, result.Error)
		mountInfo.LastError = fmt.Sprintf("Recovery mount failed: %v", result.Error)
	}
}

// GetHealthMetrics returns health metrics for monitoring
func (hc *HealthChecker) GetHealthMetrics() map[string]interface{} {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	totalMounts := len(hc.mountRegistry)
	healthyMounts := 0
	unhealthyMounts := 0

	for _, mountInfo := range hc.mountRegistry {
		if mountInfo.Healthy {
			healthyMounts++
		} else {
			unhealthyMounts++
		}
	}

	return map[string]interface{}{
		"total_mounts":     totalMounts,
		"healthy_mounts":   healthyMounts,
		"unhealthy_mounts": unhealthyMounts,
		"node_id":          hc.nodeID,
		"check_interval":   hc.checkInterval.String(),
	}
}

// CreateHealthCheckPod creates a pod to perform detailed health checks
func (hc *HealthChecker) CreateHealthCheckPod(ctx context.Context, volumeID, mountPath string) error {
	podName := fmt.Sprintf("health-check-%s-%d", volumeID, time.Now().Unix())
	namespace := "kube-system"

	// Build health check command
	healthCmd := fmt.Sprintf(`
		echo "Performing detailed health check for %s..."
		if [ -d "%s" ]; then
			echo "Mount directory exists"
			if timeout 10 ls -la "%s" > /dev/null 2>&1; then
				echo "Directory listing successful"
				if timeout 10 touch "%s/.health-check" && rm -f "%s/.health-check"; then
					echo "Write test successful"
					echo "Health check PASSED"
					exit 0
				else
					echo "Write test FAILED"
					exit 1
				fi
			else
				echo "Directory listing FAILED"
				exit 1
			fi
		else
			echo "Mount directory does not exist"
			exit 1
		fi
	`, mountPath, mountPath, mountPath, mountPath, mountPath)

	pod := &v1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: namespace,
			Labels: map[string]string{
				"app":                "xpai-csi-health-check",
				"xpai-csi/volume-id": volumeID,
				"xpai-csi/node":      hc.nodeID,
			},
		},
		Spec: v1.PodSpec{
			NodeName:      hc.nodeID,
			RestartPolicy: v1.RestartPolicyNever,
			Containers: []v1.Container{
				{
					Name:    "health-check",
					Image:   "busybox:latest",
					Command: []string{"/bin/sh"},
					Args:    []string{"-c", healthCmd},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "host-mount",
							MountPath: "/host",
						},
					},
				},
			},
			Volumes: []v1.Volume{
				{
					Name: "host-mount",
					VolumeSource: v1.VolumeSource{
						HostPath: &v1.HostPathVolumeSource{
							Path: "/",
							Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
						},
					},
				},
			},
		},
	}

	_, err := hc.kubeClient.CoreV1().Pods(namespace).Create(ctx, pod, metav1.CreateOptions{})
	return err
}
