package filesystem

import (
	"context"
	"testing"

	kubefake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestNewFilesystemManager(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	config := &ManagerConfig{
		Client:     fakeClient,
		KubeClient: fakeKubeClient,
		NodeID:     "test-node",
	}

	manager := NewFilesystemManager(config)

	if manager == nil {
		t.Fatal("Expected manager to be created, got nil")
	}

	if manager.nodeID != "test-node" {
		t.Errorf("Expected nodeID to be 'test-node', got %s", manager.nodeID)
	}

	// Check that default adapters are registered
	supportedFS := manager.GetSupportedFilesystems()
	expectedFS := []FilesystemType{
		FilesystemTypeNFS,
		FilesystemTypeCephFS,
		FilesystemTypeJuiceFS,
		FilesystemTypeGlusterFS,
		FilesystemTypeFUSE,
	}

	if len(supportedFS) != len(expectedFS) {
		t.<PERSON>("Expected %d supported filesystems, got %d", len(expectedFS), len(supportedFS))
	}

	// Check that we can get adapters
	for _, fsType := range expectedFS {
		adapter, err := manager.GetAdapter(fsType)
		if err != nil {
			t.Errorf("Failed to get adapter for %s: %v", fsType, err)
		}
		if adapter == nil {
			t.Errorf("Got nil adapter for %s", fsType)
		}
		if adapter.GetType() != fsType {
			t.Errorf("Expected adapter type %s, got %s", fsType, adapter.GetType())
		}
	}
}

func TestParseStorageClassParameters(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	manager := NewFilesystemManager(&ManagerConfig{
		Client:     fakeClient,
		KubeClient: fakeKubeClient,
		NodeID:     "test-node",
	})

	tests := []struct {
		name       string
		parameters map[string]string
		secrets    map[string]string
		expectType FilesystemType
		expectErr  bool
	}{
		{
			name: "valid NFS parameters",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "192.168.1.100",
				"path":   "/exports/data",
			},
			secrets:    map[string]string{},
			expectType: FilesystemTypeNFS,
			expectErr:  false,
		},
		{
			name: "valid CephFS parameters",
			parameters: map[string]string{
				"type":     "cephfs",
				"monitors": "192.168.1.10:6789,192.168.1.11:6789",
				"user":     "admin",
			},
			secrets:    map[string]string{},
			expectType: FilesystemTypeCephFS,
			expectErr:  false,
		},
		{
			name: "missing type parameter",
			parameters: map[string]string{
				"server": "192.168.1.100",
				"path":   "/exports/data",
			},
			secrets:   map[string]string{},
			expectErr: true,
		},
		{
			name: "unsupported filesystem type",
			parameters: map[string]string{
				"type": "unsupported",
			},
			secrets:   map[string]string{},
			expectErr: true,
		},
		{
			name: "invalid NFS parameters - missing server",
			parameters: map[string]string{
				"type": "nfs",
				"path": "/exports/data",
			},
			secrets:   map[string]string{},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fsType, mountOptions, err := manager.ParseStorageClassParameters(tt.parameters, tt.secrets)

			if tt.expectErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if fsType != tt.expectType {
				t.Errorf("Expected filesystem type %s, got %s", tt.expectType, fsType)
			}

			if mountOptions == nil {
				t.Errorf("Expected mount options but got nil")
			}
		})
	}
}

func TestGetAdapter(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	manager := NewFilesystemManager(&ManagerConfig{
		Client:     fakeClient,
		KubeClient: fakeKubeClient,
		NodeID:     "test-node",
	})

	// Test getting existing adapter
	adapter, err := manager.GetAdapter(FilesystemTypeNFS)
	if err != nil {
		t.Errorf("Failed to get NFS adapter: %v", err)
	}
	if adapter == nil {
		t.Error("Got nil adapter for NFS")
	}

	// Test getting non-existent adapter
	_, err = manager.GetAdapter("nonexistent")
	if err == nil {
		t.Error("Expected error for non-existent adapter")
	}
}

func TestStageVolume(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	manager := NewFilesystemManager(&ManagerConfig{
		Client:     fakeClient,
		KubeClient: fakeKubeClient,
		NodeID:     "test-node",
	})

	volumeCtx := &VolumeContext{
		VolumeID:   "test-volume",
		VolumeName: "test-volume",
		Parameters: map[string]string{
			"type":   "nfs",
			"server": "192.168.1.100",
			"path":   "/exports/data",
		},
		Secrets:    map[string]string{},
		Attributes: make(map[string]string),
	}

	// Parse mount options first
	fsType, mountOptions, err := manager.ParseStorageClassParameters(volumeCtx.Parameters, volumeCtx.Secrets)
	if err != nil {
		t.Fatalf("Failed to parse parameters: %v", err)
	}

	volumeCtx.MountOptions = mountOptions
	volumeCtx.Parameters["type"] = string(fsType)

	// Note: This test will fail in the actual mount operation since we don't have
	// a real NFS server, but it tests the parameter parsing and adapter selection
	ctx := context.Background()
	err = manager.StageVolume(ctx, volumeCtx, "/tmp/test-staging")

	// We expect this to fail at the mount stage, but not at parameter validation
	if err == nil {
		t.Log("Stage volume succeeded (unexpected in test environment)")
	} else {
		t.Logf("Stage volume failed as expected in test environment: %v", err)
	}
}

func TestRegisterAdapter(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	manager := NewFilesystemManager(&ManagerConfig{
		Client:     fakeClient,
		KubeClient: fakeKubeClient,
		NodeID:     "test-node",
	})

	// Create a mock adapter
	mockAdapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	// Register the adapter
	manager.RegisterAdapter(mockAdapter)

	// Verify it was registered
	adapter, err := manager.GetAdapter(FilesystemTypeNFS)
	if err != nil {
		t.Errorf("Failed to get registered adapter: %v", err)
	}
	if adapter == nil {
		t.Error("Got nil adapter after registration")
	}
}
