package filesystem

import (
	"context"
	"fmt"
	"testing"

	kubefake "k8s.io/client-go/kubernetes/fake"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
)

func TestNFSAdapter_ValidateParameters(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	tests := []struct {
		name       string
		parameters map[string]string
		expectErr  bool
	}{
		{
			name: "valid parameters",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "192.168.1.100",
				"path":   "/exports/data",
			},
			expectErr: false,
		},
		{
			name: "valid parameters with NFS version",
			parameters: map[string]string{
				"type":       "nfs",
				"server":     "192.168.1.100",
				"path":       "/exports/data",
				"nfsVersion": "4",
			},
			expectErr: false,
		},
		{
			name: "missing server",
			parameters: map[string]string{
				"type": "nfs",
				"path": "/exports/data",
			},
			expectErr: true,
		},
		{
			name: "missing path",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "192.168.1.100",
			},
			expectErr: true,
		},
		{
			name: "invalid NFS version",
			parameters: map[string]string{
				"type":       "nfs",
				"server":     "192.168.1.100",
				"path":       "/exports/data",
				"nfsVersion": "invalid",
			},
			expectErr: true,
		},
		{
			name: "empty server",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "",
				"path":   "/exports/data",
			},
			expectErr: true,
		},
		{
			name: "empty path",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "192.168.1.100",
				"path":   "",
			},
			expectErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := adapter.ValidateParameters(tt.parameters)

			if tt.expectErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

func TestNFSAdapter_ParseMountOptions(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	tests := []struct {
		name       string
		parameters map[string]string
		secrets    map[string]string
		expectErr  bool
		checkFunc  func(*MountOptions) error
	}{
		{
			name: "basic parameters",
			parameters: map[string]string{
				"type":   "nfs",
				"server": "192.168.1.100",
				"path":   "/exports/data",
			},
			secrets:   map[string]string{},
			expectErr: false,
			checkFunc: func(opts *MountOptions) error {
				if opts.Server != "192.168.1.100" {
					return fmt.Errorf("expected server '192.168.1.100', got '%s'", opts.Server)
				}
				if opts.Path != "/exports/data" {
					return fmt.Errorf("expected path '/exports/data', got '%s'", opts.Path)
				}
				if opts.NFSVersion != "4" {
					return fmt.Errorf("expected default NFS version '4', got '%s'", opts.NFSVersion)
				}
				return nil
			},
		},
		{
			name: "with NFS version",
			parameters: map[string]string{
				"type":       "nfs",
				"server":     "192.168.1.100",
				"path":       "/exports/data",
				"nfsVersion": "3",
			},
			secrets:   map[string]string{},
			expectErr: false,
			checkFunc: func(opts *MountOptions) error {
				if opts.NFSVersion != "3" {
					return fmt.Errorf("expected NFS version '3', got '%s'", opts.NFSVersion)
				}
				return nil
			},
		},
		{
			name: "with mount options",
			parameters: map[string]string{
				"type":         "nfs",
				"server":       "192.168.1.100",
				"path":         "/exports/data",
				"mountOptions": "hard,intr,timeo=600",
			},
			secrets:   map[string]string{},
			expectErr: false,
			checkFunc: func(opts *MountOptions) error {
				expectedFlags := []string{"hard", "intr", "timeo=600"}
				if len(opts.MountFlags) < len(expectedFlags) {
					return fmt.Errorf("expected at least %d mount flags, got %d", len(expectedFlags), len(opts.MountFlags))
				}
				return nil
			},
		},
		{
			name: "with read-only",
			parameters: map[string]string{
				"type":     "nfs",
				"server":   "192.168.1.100",
				"path":     "/exports/data",
				"readOnly": "true",
			},
			secrets:   map[string]string{},
			expectErr: false,
			checkFunc: func(opts *MountOptions) error {
				if !opts.ReadOnly {
					return fmt.Errorf("expected readOnly to be true")
				}
				return nil
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			opts, err := adapter.ParseMountOptions(tt.parameters, tt.secrets)

			if tt.expectErr {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if opts == nil {
				t.Errorf("Expected mount options but got nil")
				return
			}

			if tt.checkFunc != nil {
				if err := tt.checkFunc(opts); err != nil {
					t.Errorf("Mount options check failed: %v", err)
				}
			}
		})
	}
}

func TestNFSAdapter_GenerateMountPodSpec(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	volumeCtx := &VolumeContext{
		VolumeID:   "test-volume",
		VolumeName: "test-volume",
		Parameters: map[string]string{
			"type":   "nfs",
			"server": "192.168.1.100",
			"path":   "/exports/data",
		},
		MountOptions: &MountOptions{
			Server:     "192.168.1.100",
			Path:       "/exports/data",
			NFSVersion: "4",
		},
	}

	ctx := context.Background()
	spec, err := adapter.GenerateMountPodSpec(ctx, volumeCtx, "/tmp/staging", "/tmp/target")

	if err != nil {
		t.Errorf("Failed to generate mount pod spec: %v", err)
		return
	}

	if spec == nil {
		t.Error("Expected mount pod spec but got nil")
		return
	}

	// Check basic pod spec properties
	if spec.Name == "" {
		t.Error("Expected pod name to be set")
	}

	if spec.Namespace == "" {
		t.Error("Expected pod namespace to be set")
	}

	if spec.Image == "" {
		t.Error("Expected pod image to be set")
	}

	if len(spec.Command) == 0 {
		t.Error("Expected pod command to be set")
	}

	if len(spec.Args) == 0 {
		t.Error("Expected pod args to be set")
	}

	// Check that security context is set for privileged access
	if spec.SecurityContext == nil {
		t.Error("Expected security context to be set")
	} else {
		if spec.SecurityContext.Privileged == nil || !*spec.SecurityContext.Privileged {
			t.Error("Expected pod to be privileged")
		}
	}

	// Check volume mounts
	if len(spec.VolumeMounts) == 0 {
		t.Error("Expected volume mounts to be set")
	}

	// Check volumes
	if len(spec.Volumes) == 0 {
		t.Error("Expected volumes to be set")
	}
}

func TestNFSAdapter_GetType(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	if adapter.GetType() != FilesystemTypeNFS {
		t.Errorf("Expected filesystem type %s, got %s", FilesystemTypeNFS, adapter.GetType())
	}
}

func TestNFSAdapter_HealthCheck(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	ctx := context.Background()

	// Test with non-existent path
	err := adapter.HealthCheck(ctx, "/non/existent/path")
	if err == nil {
		t.Error("Expected error for non-existent path")
	}

	// Test with existing path (create a temporary directory)
	tmpDir := t.TempDir()
	err = adapter.HealthCheck(ctx, tmpDir)
	if err != nil {
		t.Errorf("Unexpected error for existing path: %v", err)
	}
}

func TestNFSAdapter_GetMountInfo(t *testing.T) {
	fakeClient := fake.NewClientBuilder().Build()
	fakeKubeClient := kubefake.NewSimpleClientset()

	adapter := NewNFSAdapter(fakeClient, fakeKubeClient, "test-node")

	ctx := context.Background()
	mountPath := "/tmp/test-mount"

	info, err := adapter.GetMountInfo(ctx, mountPath)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
		return
	}

	if info == nil {
		t.Error("Expected mount info but got nil")
		return
	}

	if info["filesystem"] != "nfs" {
		t.Errorf("Expected filesystem 'nfs', got '%s'", info["filesystem"])
	}

	if info["mountPath"] != mountPath {
		t.Errorf("Expected mountPath '%s', got '%s'", mountPath, info["mountPath"])
	}
}
