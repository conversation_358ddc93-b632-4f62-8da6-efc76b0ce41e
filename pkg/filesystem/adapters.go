package filesystem

import (
	"context"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// FUSEAdapter implements FilesystemAdapter for generic FUSE filesystems
type FUSEAdapter struct {
	*BaseAdapter
}

// NewFUSEAdapter creates a new FUSE adapter
func NewFUSEAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &FUSEAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeFUSE),
	}
}

func (f *FUSEAdapter) ValidateParameters(parameters map[string]string) error {
	if err := f.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["command"]; !exists {
		return fmt.Errorf("missing required parameter 'command' for FUSE")
	}

	return nil
}

func (f *FUSEAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := f.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	// Store the FUSE command in extra options
	options.ExtraOptions["command"] = parameters["command"]

	if args, exists := parameters["args"]; exists {
		options.ExtraOptions["args"] = args
	}

	return options, nil
}

func (f *FUSEAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := f.GeneratePodName(volumeCtx.VolumeID)
	namespace := f.GetMountNamespace()

	command := volumeCtx.MountOptions.ExtraOptions["command"]
	args := volumeCtx.MountOptions.ExtraOptions["args"]

	var mountCmd string
	if args != "" {
		mountCmd = fmt.Sprintf("mkdir -p %s && %s %s %s", targetPath, command, args, targetPath)
	} else {
		mountCmd = fmt.Sprintf("mkdir -p %s && %s %s", targetPath, command, targetPath)
	}

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     f.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}, nil
}

// Implement other required methods for FUSEAdapter
func (f *FUSEAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error { return nil }
func (f *FUSEAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	return &MountResult{Success: true, MountPath: targetPath}, nil
}
func (f *FUSEAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}
func (f *FUSEAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}
func (f *FUSEAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}
func (f *FUSEAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "fuse", "mountPath": mountPath}, nil
}
