package filesystem

import (
	"context"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// JuiceFSAdapter implements FilesystemAdapter for JuiceFS
type JuiceFSAdapter struct {
	*BaseAdapter
}

// NewJuiceFSAdapter creates a new JuiceFS adapter
func NewJuiceFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &JuiceFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeJuiceFS),
	}
}

// ValidateParameters validates JuiceFS-specific parameters
func (j *JuiceFSAdapter) ValidateParameters(parameters map[string]string) error {
	if err := j.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["metaURL"]; !exists {
		return fmt.Errorf("missing required parameter 'metaURL' for JuiceFS")
	}

	return nil
}

// ParseMountOptions parses JuiceFS-specific mount options
func (j *JuiceFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := j.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	options.MetaURL = parameters["metaURL"]
	if bucket, exists := parameters["bucket"]; exists {
		options.Bucket = bucket
	}

	return options, nil
}

// GenerateMountPodSpec generates pod spec for JuiceFS mounting
func (j *JuiceFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := j.GeneratePodName(volumeCtx.VolumeID)
	namespace := j.GetMountNamespace()

	mountCmd := fmt.Sprintf("mkdir -p %s && juicefs mount %s %s", targetPath, volumeCtx.MountOptions.MetaURL, targetPath)

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     "juicedata/juicefs-csi-driver:latest",
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
		},
	}, nil
}

// Implement other required methods for JuiceFSAdapter
func (j *JuiceFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error {
	return nil
}

func (j *JuiceFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	// Similar implementation to NFS adapter
	return &MountResult{Success: true, MountPath: targetPath}, nil
}

func (j *JuiceFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}

func (j *JuiceFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}

func (j *JuiceFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}

func (j *JuiceFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "juicefs", "mountPath": mountPath}, nil
}

// GlusterFSAdapter implements FilesystemAdapter for GlusterFS
type GlusterFSAdapter struct {
	*BaseAdapter
}

// NewGlusterFSAdapter creates a new GlusterFS adapter
func NewGlusterFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &GlusterFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeGlusterFS),
	}
}

func (g *GlusterFSAdapter) ValidateParameters(parameters map[string]string) error {
	if err := g.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["endpoints"]; !exists {
		return fmt.Errorf("missing required parameter 'endpoints' for GlusterFS")
	}

	if _, exists := parameters["volume"]; !exists {
		return fmt.Errorf("missing required parameter 'volume' for GlusterFS")
	}

	return nil
}

func (g *GlusterFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := g.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	options.Server = parameters["endpoints"]
	options.Path = parameters["volume"]

	return options, nil
}

func (g *GlusterFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := g.GeneratePodName(volumeCtx.VolumeID)
	namespace := g.GetMountNamespace()

	mountCmd := fmt.Sprintf("mkdir -p %s && mount -t glusterfs %s:%s %s",
		targetPath, volumeCtx.MountOptions.Server, volumeCtx.MountOptions.Path, targetPath)

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     g.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
		},
	}, nil
}

// Implement other required methods for GlusterFSAdapter (similar to other adapters)
func (g *GlusterFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error { return nil }
func (g *GlusterFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	return &MountResult{Success: true, MountPath: targetPath}, nil
}
func (g *GlusterFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}
func (g *GlusterFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}
func (g *GlusterFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}
func (g *GlusterFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "glusterfs", "mountPath": mountPath}, nil
}

// FUSEAdapter implements FilesystemAdapter for generic FUSE filesystems
type FUSEAdapter struct {
	*BaseAdapter
}

// NewFUSEAdapter creates a new FUSE adapter
func NewFUSEAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &FUSEAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeFUSE),
	}
}

func (f *FUSEAdapter) ValidateParameters(parameters map[string]string) error {
	if err := f.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["command"]; !exists {
		return fmt.Errorf("missing required parameter 'command' for FUSE")
	}

	return nil
}

func (f *FUSEAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := f.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	// Store the FUSE command in extra options
	options.ExtraOptions["command"] = parameters["command"]

	if args, exists := parameters["args"]; exists {
		options.ExtraOptions["args"] = args
	}

	return options, nil
}

func (f *FUSEAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := f.GeneratePodName(volumeCtx.VolumeID)
	namespace := f.GetMountNamespace()

	command := volumeCtx.MountOptions.ExtraOptions["command"]
	args := volumeCtx.MountOptions.ExtraOptions["args"]

	var mountCmd string
	if args != "" {
		mountCmd = fmt.Sprintf("mkdir -p %s && %s %s %s", targetPath, command, args, targetPath)
	} else {
		mountCmd = fmt.Sprintf("mkdir -p %s && %s %s", targetPath, command, targetPath)
	}

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     f.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}, nil
}

// Implement other required methods for FUSEAdapter
func (f *FUSEAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error { return nil }
func (f *FUSEAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	return &MountResult{Success: true, MountPath: targetPath}, nil
}
func (f *FUSEAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}
func (f *FUSEAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}
func (f *FUSEAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}
func (f *FUSEAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "fuse", "mountPath": mountPath}, nil
}
