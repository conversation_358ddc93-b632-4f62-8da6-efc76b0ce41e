package filesystem

import (
	"context"
	"time"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// FilesystemType represents the type of filesystem
type FilesystemType string

const (
	// Supported filesystem types
	FilesystemTypeNFS       FilesystemType = "nfs"
	FilesystemTypeCephFS    FilesystemType = "cephfs"
	FilesystemTypeJuiceFS   FilesystemType = "juicefs"
	FilesystemTypeGlusterFS FilesystemType = "glusterfs"
	FilesystemTypeFUSE      FilesystemType = "fuse"
)

// MountOptions contains options for mounting a filesystem
type MountOptions struct {
	// Common options
	ReadOnly     bool              `json:"readOnly,omitempty"`
	MountFlags   []string          `json:"mountFlags,omitempty"`
	ExtraOptions map[string]string `json:"extraOptions,omitempty"`

	// Filesystem specific options
	Server  string `json:"server,omitempty"`  // For NFS, GlusterFS
	Path    string `json:"path,omitempty"`    // Mount path
	SubPath string `json:"subPath,omitempty"` // Sub directory

	// NFS specific
	NFSVersion string `json:"nfsVersion,omitempty"`

	// CephFS specific
	Monitors  []string `json:"monitors,omitempty"`
	User      string   `json:"user,omitempty"`
	SecretRef string   `json:"secretRef,omitempty"`

	// JuiceFS specific
	MetaURL   string `json:"metaURL,omitempty"`
	AccessKey string `json:"accessKey,omitempty"`
	SecretKey string `json:"secretKey,omitempty"`
	Bucket    string `json:"bucket,omitempty"`
}

// VolumeContext contains volume-specific information
type VolumeContext struct {
	VolumeID         string            `json:"volumeId"`
	VolumeName       string            `json:"volumeName"`
	VolumeSize       int64             `json:"volumeSize"`
	Parameters       map[string]string `json:"parameters"`
	Secrets          map[string]string `json:"secrets"`
	VolumeContext    map[string]string `json:"volumeContext"`
	MountOptions     *MountOptions     `json:"mountOptions"`
	Attributes       map[string]string `json:"attributes"`
	PVCName          string            `json:"pvcName,omitempty"`          // PVC name for cross-namespace sharing
	PVCNamespace     string            `json:"pvcNamespace,omitempty"`     // PVC namespace
	StorageClassName string            `json:"storageClassName,omitempty"` // StorageClass name for validation
}

// MountPodSpec contains specifications for mount pod
type MountPodSpec struct {
	Name            string                  `json:"name"`
	Namespace       string                  `json:"namespace"`
	Image           string                  `json:"image"`
	Command         []string                `json:"command"`
	Args            []string                `json:"args"`
	Env             []v1.EnvVar             `json:"env,omitempty"`
	VolumeMounts    []v1.VolumeMount        `json:"volumeMounts,omitempty"`
	Volumes         []v1.Volume             `json:"volumes,omitempty"`
	Resources       v1.ResourceRequirements `json:"resources,omitempty"`
	NodeSelector    map[string]string       `json:"nodeSelector,omitempty"`
	Tolerations     []v1.Toleration         `json:"tolerations,omitempty"`
	SecurityContext *v1.SecurityContext     `json:"securityContext,omitempty"`
}

// MountResult contains the result of a mount operation
type MountResult struct {
	Success      bool              `json:"success"`
	MountPath    string            `json:"mountPath"`
	MountPodName string            `json:"mountPodName,omitempty"`
	Error        error             `json:"error,omitempty"`
	Metadata     map[string]string `json:"metadata,omitempty"`
}

// FilesystemAdapter defines the interface for filesystem adapters
type FilesystemAdapter interface {
	// GetType returns the filesystem type this adapter handles
	GetType() FilesystemType

	// ValidateParameters validates the storage class parameters
	ValidateParameters(parameters map[string]string) error

	// ParseMountOptions parses storage class parameters into mount options
	ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error)

	// GenerateMountPodSpec generates the pod specification for mounting
	GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error)

	// PreMount performs any pre-mount operations (e.g., creating directories, checking connectivity)
	PreMount(ctx context.Context, volumeCtx *VolumeContext) error

	// Mount performs the actual mount operation using mount pod
	Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error)

	// Unmount performs the unmount operation
	Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error

	// PostMount performs any post-mount operations (e.g., setting permissions)
	PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error

	// HealthCheck checks if the mount is healthy
	HealthCheck(ctx context.Context, mountPath string) error

	// GetMountInfo returns information about the mount
	GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error)
}

// FilesystemManager manages different filesystem adapters
type FilesystemManager struct {
	adapters                    map[FilesystemType]FilesystemAdapter
	client                      client.Client
	kubeClient                  kubernetes.Interface
	nodeID                      string
	mountTimeout                time.Duration
	mountManager                *MountManager
	enableSharedMounts          bool
	enableCrossNamespaceSharing bool
}

// ManagerConfig contains configuration for FilesystemManager
type ManagerConfig struct {
	Client                      client.Client
	KubeClient                  kubernetes.Interface
	NodeID                      string
	MountTimeout                time.Duration
	EnableSharedMounts          bool
	SharedMountDir              string
	EnableCrossNamespaceSharing bool
}
