package filesystem

import (
	"context"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// GlusterFSAdapter implements FilesystemAdapter for GlusterFS
type GlusterFSAdapter struct {
	*BaseAdapter
}

// NewGlusterFSAdapter creates a new GlusterFS adapter
func NewGlusterFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &GlusterFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeGlusterFS),
	}
}

func (g *GlusterFSAdapter) ValidateParameters(parameters map[string]string) error {
	if err := g.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["endpoints"]; !exists {
		return fmt.Errorf("missing required parameter 'endpoints' for GlusterFS")
	}

	if _, exists := parameters["volume"]; !exists {
		return fmt.Errorf("missing required parameter 'volume' for GlusterFS")
	}

	return nil
}

func (g *GlusterFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := g.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	options.Server = parameters["endpoints"]
	options.Path = parameters["volume"]

	return options, nil
}

func (g *GlusterFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := g.GeneratePodName(volumeCtx.VolumeID)
	namespace := g.GetMountNamespace()

	mountCmd := fmt.Sprintf("mkdir -p %s && mount -t glusterfs %s:%s %s",
		targetPath, volumeCtx.MountOptions.Server, volumeCtx.MountOptions.Path, targetPath)

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     g.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
		},
	}, nil
}

// Implement other required methods for GlusterFSAdapter (similar to other adapters)
func (g *GlusterFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error { return nil }
func (g *GlusterFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	return &MountResult{Success: true, MountPath: targetPath}, nil
}
func (g *GlusterFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}
func (g *GlusterFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}
func (g *GlusterFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}
func (g *GlusterFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "glusterfs", "mountPath": mountPath}, nil
}
