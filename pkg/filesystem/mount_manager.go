package filesystem

import (
	"context"
	"crypto/sha256"
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"github.com/golang/glog"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// SharedMountInfo contains information about a shared mount
type SharedMountInfo struct {
	MountKey       string          `json:"mountKey"`   // Unique key for the mount configuration
	SharedPath     string          `json:"sharedPath"` // Shared mount path on the host
	FilesystemType string          `json:"filesystemType"`
	MountOptions   *MountOptions   `json:"mountOptions"`
	RefCount       int             `json:"refCount"`  // Number of volumes using this mount
	VolumeIDs      map[string]bool `json:"volumeIds"` // Set of volume IDs using this mount
	CreatedAt      time.Time       `json:"createdAt"`
	LastUsed       time.Time       `json:"lastUsed"`
	MountPodName   string          `json:"mountPodName,omitempty"`
}

// MountManager manages shared mounts for the same filesystem configuration
type MountManager struct {
	client       client.Client
	kubeClient   kubernetes.Interface
	nodeID       string
	sharedMounts map[string]*SharedMountInfo // mountKey -> SharedMountInfo
	mutex        sync.RWMutex
	sharedDir    string // Base directory for shared mounts
}

// MountManagerConfig contains configuration for MountManager
type MountManagerConfig struct {
	Client     client.Client
	KubeClient kubernetes.Interface
	NodeID     string
	SharedDir  string // Base directory for shared mounts, default: /var/lib/xpai-csi/shared
}

// NewMountManager creates a new mount manager
func NewMountManager(config *MountManagerConfig) *MountManager {
	if config.SharedDir == "" {
		config.SharedDir = "/var/lib/xpai-csi/shared"
	}

	return &MountManager{
		client:       config.Client,
		kubeClient:   config.KubeClient,
		nodeID:       config.NodeID,
		sharedMounts: make(map[string]*SharedMountInfo),
		sharedDir:    config.SharedDir,
	}
}

// GenerateMountKey generates a unique key for mount configuration
func (mm *MountManager) GenerateMountKey(fsType FilesystemType, options *MountOptions) string {
	// Create a hash based on filesystem type and mount options
	h := sha256.New()
	h.Write([]byte(string(fsType)))

	// Add server and path for network filesystems
	if options.Server != "" {
		h.Write([]byte(options.Server))
	}
	if options.Path != "" {
		h.Write([]byte(options.Path))
	}

	// Add specific options based on filesystem type
	switch fsType {
	case FilesystemTypeNFS:
		if options.NFSVersion != "" {
			h.Write([]byte(options.NFSVersion))
		}
	case FilesystemTypeCephFS:
		if len(options.Monitors) > 0 {
			for _, monitor := range options.Monitors {
				h.Write([]byte(monitor))
			}
		}
		if options.User != "" {
			h.Write([]byte(options.User))
		}
	case FilesystemTypeJuiceFS:
		if options.MetaURL != "" {
			h.Write([]byte(options.MetaURL))
		}
		if options.Bucket != "" {
			h.Write([]byte(options.Bucket))
		}
	}

	// Add mount flags
	for _, flag := range options.MountFlags {
		h.Write([]byte(flag))
	}

	return fmt.Sprintf("%x", h.Sum(nil))[:16] // Use first 16 characters
}

// GenerateCrossNamespaceMountKey generates a mount key for cross-namespace PVC sharing
// This key is based on PVC name and StorageClass parameters, not volumeID
func (mm *MountManager) GenerateCrossNamespaceMountKey(pvcName string, fsType FilesystemType, options *MountOptions) string {
	// Create a hash based on PVC name and filesystem configuration
	h := sha256.New()

	// Add PVC name as the primary identifier for cross-namespace sharing
	h.Write([]byte(pvcName))
	h.Write([]byte(string(fsType)))

	// Add server and path for network filesystems
	if options.Server != "" {
		h.Write([]byte(options.Server))
	}
	if options.Path != "" {
		h.Write([]byte(options.Path))
	}

	// Add specific options based on filesystem type
	switch fsType {
	case FilesystemTypeNFS:
		if options.NFSVersion != "" {
			h.Write([]byte(options.NFSVersion))
		}
	case FilesystemTypeCephFS:
		if len(options.Monitors) > 0 {
			for _, monitor := range options.Monitors {
				h.Write([]byte(monitor))
			}
		}
		if options.User != "" {
			h.Write([]byte(options.User))
		}
	case FilesystemTypeJuiceFS:
		if options.MetaURL != "" {
			h.Write([]byte(options.MetaURL))
		}
		if options.Bucket != "" {
			h.Write([]byte(options.Bucket))
		}
	}

	// Add mount flags
	for _, flag := range options.MountFlags {
		h.Write([]byte(flag))
	}

	return fmt.Sprintf("cross-%s-%x", pvcName, h.Sum(nil))[:32] // Use PVC name prefix
}

// GetSharedMountPath returns the shared mount path for a given mount key
func (mm *MountManager) GetSharedMountPath(mountKey string) string {
	return filepath.Join(mm.sharedDir, mountKey)
}

// AcquireSharedMount acquires a shared mount for the given volume
func (mm *MountManager) AcquireSharedMount(ctx context.Context, volumeID string, fsType FilesystemType, options *MountOptions, adapter FilesystemAdapter) (string, error) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	mountKey := mm.GenerateMountKey(fsType, options)
	sharedPath := mm.GetSharedMountPath(mountKey)

	glog.V(2).Infof("Acquiring shared mount for volume %s, mountKey: %s, sharedPath: %s", volumeID, mountKey, sharedPath)

	// Check if shared mount already exists
	if sharedMount, exists := mm.sharedMounts[mountKey]; exists {
		// Mount already exists, increment reference count
		sharedMount.RefCount++
		sharedMount.VolumeIDs[volumeID] = true
		sharedMount.LastUsed = time.Now()

		glog.V(2).Infof("Reusing existing shared mount %s for volume %s (refCount: %d)", mountKey, volumeID, sharedMount.RefCount)
		return sharedPath, nil
	}

	// Create new shared mount
	glog.V(2).Infof("Creating new shared mount %s for volume %s", mountKey, volumeID)

	// Create shared mount directory
	if err := ensureDirectory(sharedPath); err != nil {
		return "", fmt.Errorf("failed to create shared mount directory: %v", err)
	}

	// Create volume context for mounting
	volumeCtx := &VolumeContext{
		VolumeID:     fmt.Sprintf("shared-%s", mountKey),
		VolumeName:   fmt.Sprintf("shared-%s", mountKey),
		Parameters:   map[string]string{"type": string(fsType)},
		MountOptions: options,
		Attributes:   make(map[string]string),
	}

	// Perform the actual mount
	result, err := adapter.Mount(ctx, volumeCtx, sharedPath, sharedPath)
	if err != nil {
		return "", fmt.Errorf("failed to create shared mount: %v", err)
	}

	if !result.Success {
		return "", fmt.Errorf("shared mount operation failed: %v", result.Error)
	}

	// Register the shared mount
	sharedMount := &SharedMountInfo{
		MountKey:       mountKey,
		SharedPath:     sharedPath,
		FilesystemType: string(fsType),
		MountOptions:   options,
		RefCount:       1,
		VolumeIDs:      map[string]bool{volumeID: true},
		CreatedAt:      time.Now(),
		LastUsed:       time.Now(),
		MountPodName:   result.MountPodName,
	}

	mm.sharedMounts[mountKey] = sharedMount

	glog.V(2).Infof("Successfully created shared mount %s for volume %s", mountKey, volumeID)
	return sharedPath, nil
}

// ReleaseSharedMount releases a shared mount for the given volume
func (mm *MountManager) ReleaseSharedMount(ctx context.Context, volumeID string, fsType FilesystemType, options *MountOptions, adapter FilesystemAdapter) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	mountKey := mm.GenerateMountKey(fsType, options)

	glog.V(2).Infof("Releasing shared mount for volume %s, mountKey: %s", volumeID, mountKey)

	sharedMount, exists := mm.sharedMounts[mountKey]
	if !exists {
		glog.Warningf("Shared mount %s not found for volume %s", mountKey, volumeID)
		return nil
	}

	// Remove volume from the shared mount
	delete(sharedMount.VolumeIDs, volumeID)
	sharedMount.RefCount--

	glog.V(2).Infof("Released shared mount %s for volume %s (refCount: %d)", mountKey, volumeID, sharedMount.RefCount)

	// If no more volumes are using this mount, unmount it
	if sharedMount.RefCount <= 0 {
		glog.V(2).Infof("Unmounting shared mount %s as no volumes are using it", mountKey)

		// Create volume context for unmounting
		volumeCtx := &VolumeContext{
			VolumeID:     fmt.Sprintf("shared-%s", mountKey),
			VolumeName:   fmt.Sprintf("shared-%s", mountKey),
			Parameters:   map[string]string{"type": sharedMount.FilesystemType},
			MountOptions: sharedMount.MountOptions,
			Attributes:   make(map[string]string),
		}

		// Unmount the shared mount
		if err := adapter.Unmount(ctx, volumeCtx, sharedMount.SharedPath); err != nil {
			glog.Errorf("Failed to unmount shared mount %s: %v", mountKey, err)
			// Don't return error, just log it
		}

		// Remove from registry
		delete(mm.sharedMounts, mountKey)

		glog.V(2).Infof("Successfully removed shared mount %s", mountKey)
	}

	return nil
}

// GetSharedMountInfo returns information about a shared mount
func (mm *MountManager) GetSharedMountInfo(mountKey string) (*SharedMountInfo, bool) {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	info, exists := mm.sharedMounts[mountKey]
	if !exists {
		return nil, false
	}

	// Return a copy to avoid race conditions
	infoCopy := *info
	infoCopy.VolumeIDs = make(map[string]bool)
	for k, v := range info.VolumeIDs {
		infoCopy.VolumeIDs[k] = v
	}

	return &infoCopy, true
}

// ListSharedMounts returns all shared mounts
func (mm *MountManager) ListSharedMounts() map[string]*SharedMountInfo {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	result := make(map[string]*SharedMountInfo)
	for key, info := range mm.sharedMounts {
		infoCopy := *info
		infoCopy.VolumeIDs = make(map[string]bool)
		for k, v := range info.VolumeIDs {
			infoCopy.VolumeIDs[k] = v
		}
		result[key] = &infoCopy
	}

	return result
}

// CleanupStaleSharedMounts cleans up shared mounts that haven't been used for a while
func (mm *MountManager) CleanupStaleSharedMounts(ctx context.Context, maxAge time.Duration, adapter FilesystemAdapter) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	now := time.Now()
	var toDelete []string

	for mountKey, sharedMount := range mm.sharedMounts {
		if sharedMount.RefCount <= 0 && now.Sub(sharedMount.LastUsed) > maxAge {
			toDelete = append(toDelete, mountKey)
		}
	}

	for _, mountKey := range toDelete {
		sharedMount := mm.sharedMounts[mountKey]
		glog.V(2).Infof("Cleaning up stale shared mount %s", mountKey)

		// Create volume context for unmounting
		volumeCtx := &VolumeContext{
			VolumeID:     fmt.Sprintf("shared-%s", mountKey),
			VolumeName:   fmt.Sprintf("shared-%s", mountKey),
			Parameters:   map[string]string{"type": sharedMount.FilesystemType},
			MountOptions: sharedMount.MountOptions,
			Attributes:   make(map[string]string),
		}

		// Unmount the shared mount
		if err := adapter.Unmount(ctx, volumeCtx, sharedMount.SharedPath); err != nil {
			glog.Errorf("Failed to unmount stale shared mount %s: %v", mountKey, err)
		}

		delete(mm.sharedMounts, mountKey)
	}

	return nil
}

// GetMetrics returns metrics about shared mounts
func (mm *MountManager) GetMetrics() map[string]interface{} {
	mm.mutex.RLock()
	defer mm.mutex.RUnlock()

	totalMounts := len(mm.sharedMounts)
	totalVolumes := 0

	for _, sharedMount := range mm.sharedMounts {
		totalVolumes += len(sharedMount.VolumeIDs)
	}

	return map[string]interface{}{
		"total_shared_mounts": totalMounts,
		"total_volumes":       totalVolumes,
		"node_id":             mm.nodeID,
		"shared_directory":    mm.sharedDir,
	}
}

// AcquireCrossNamespaceSharedMount acquires a shared mount for cross-namespace PVC sharing
func (mm *MountManager) AcquireCrossNamespaceSharedMount(ctx context.Context, pvcName, volumeID string, fsType FilesystemType, options *MountOptions, adapter FilesystemAdapter) (string, error) {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	mountKey := mm.GenerateCrossNamespaceMountKey(pvcName, fsType, options)
	sharedPath := mm.GetSharedMountPath(mountKey)

	glog.V(2).Infof("Acquiring cross-namespace shared mount for PVC %s (volume %s), mountKey: %s, sharedPath: %s", pvcName, volumeID, mountKey, sharedPath)

	// Check if shared mount already exists
	if sharedMount, exists := mm.sharedMounts[mountKey]; exists {
		// Mount already exists, increment reference count
		sharedMount.RefCount++
		sharedMount.VolumeIDs[volumeID] = true
		sharedMount.LastUsed = time.Now()

		glog.V(2).Infof("Reusing existing cross-namespace shared mount %s for PVC %s (volume %s) (refCount: %d)", mountKey, pvcName, volumeID, sharedMount.RefCount)
		return sharedPath, nil
	}

	// Create new shared mount with PVC-specific subdirectory
	glog.V(2).Infof("Creating new cross-namespace shared mount %s for PVC %s (volume %s)", mountKey, pvcName, volumeID)

	// Create shared mount directory
	if err := ensureDirectory(sharedPath); err != nil {
		return "", fmt.Errorf("failed to create shared mount directory: %v", err)
	}

	// Modify the mount path to include PVC-specific subdirectory
	pvcSubPath := filepath.Join(options.Path, pvcName)
	modifiedOptions := *options
	modifiedOptions.Path = pvcSubPath

	// Create volume context for mounting
	volumeCtx := &VolumeContext{
		VolumeID:     fmt.Sprintf("cross-shared-%s", mountKey),
		VolumeName:   fmt.Sprintf("cross-shared-%s", pvcName),
		Parameters:   map[string]string{"type": string(fsType)},
		MountOptions: &modifiedOptions,
		Attributes:   make(map[string]string),
	}

	// Perform the actual mount
	result, err := adapter.Mount(ctx, volumeCtx, sharedPath, sharedPath)
	if err != nil {
		return "", fmt.Errorf("failed to create cross-namespace shared mount: %v", err)
	}

	if !result.Success {
		return "", fmt.Errorf("cross-namespace shared mount operation failed: %v", result.Error)
	}

	// Register the shared mount
	sharedMount := &SharedMountInfo{
		MountKey:       mountKey,
		SharedPath:     sharedPath,
		FilesystemType: string(fsType),
		MountOptions:   &modifiedOptions,
		RefCount:       1,
		VolumeIDs:      map[string]bool{volumeID: true},
		CreatedAt:      time.Now(),
		LastUsed:       time.Now(),
		MountPodName:   result.MountPodName,
	}

	mm.sharedMounts[mountKey] = sharedMount

	glog.V(2).Infof("Successfully created cross-namespace shared mount %s for PVC %s (volume %s) at subpath %s", mountKey, pvcName, volumeID, pvcSubPath)
	return sharedPath, nil
}

// ReleaseCrossNamespaceSharedMount releases a cross-namespace shared mount
func (mm *MountManager) ReleaseCrossNamespaceSharedMount(ctx context.Context, pvcName, volumeID string, fsType FilesystemType, options *MountOptions, adapter FilesystemAdapter) error {
	mm.mutex.Lock()
	defer mm.mutex.Unlock()

	mountKey := mm.GenerateCrossNamespaceMountKey(pvcName, fsType, options)

	glog.V(2).Infof("Releasing cross-namespace shared mount for PVC %s (volume %s), mountKey: %s", pvcName, volumeID, mountKey)

	sharedMount, exists := mm.sharedMounts[mountKey]
	if !exists {
		glog.Warningf("Cross-namespace shared mount %s not found for PVC %s (volume %s)", mountKey, pvcName, volumeID)
		return nil
	}

	// Remove volume from the shared mount
	delete(sharedMount.VolumeIDs, volumeID)
	sharedMount.RefCount--

	glog.V(2).Infof("Released cross-namespace shared mount %s for PVC %s (volume %s) (refCount: %d)", mountKey, pvcName, volumeID, sharedMount.RefCount)

	// If no more volumes are using this mount, unmount it
	if sharedMount.RefCount <= 0 {
		glog.V(2).Infof("Unmounting cross-namespace shared mount %s as no volumes are using it", mountKey)

		// Create volume context for unmounting
		volumeCtx := &VolumeContext{
			VolumeID:     fmt.Sprintf("cross-shared-%s", mountKey),
			VolumeName:   fmt.Sprintf("cross-shared-%s", pvcName),
			Parameters:   map[string]string{"type": sharedMount.FilesystemType},
			MountOptions: sharedMount.MountOptions,
			Attributes:   make(map[string]string),
		}

		// Unmount the shared mount
		if err := adapter.Unmount(ctx, volumeCtx, sharedMount.SharedPath); err != nil {
			glog.Errorf("Failed to unmount cross-namespace shared mount %s: %v", mountKey, err)
			// Don't return error, just log it
		}

		// Remove from registry
		delete(mm.sharedMounts, mountKey)

		glog.V(2).Infof("Successfully removed cross-namespace shared mount %s", mountKey)
	}

	return nil
}

// ensureDirectory creates a directory if it doesn't exist
func ensureDirectory(path string) error {
	// This is a simplified implementation
	// In production, you might want to use proper directory creation with permissions
	return nil
}
