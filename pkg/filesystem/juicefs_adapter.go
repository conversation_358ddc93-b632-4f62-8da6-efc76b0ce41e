package filesystem

import (
	"context"
	"fmt"
	"os"

	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// JuiceFSAdapter implements FilesystemAdapter for JuiceFS
type JuiceFSAdapter struct {
	*BaseAdapter
}

// NewJuiceFSAdapter creates a new JuiceFS adapter
func NewJuiceFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &JuiceFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeJuiceFS),
	}
}

// ValidateParameters validates JuiceFS-specific parameters
func (j *JuiceFSAdapter) ValidateParameters(parameters map[string]string) error {
	if err := j.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	if _, exists := parameters["metaURL"]; !exists {
		return fmt.Errorf("missing required parameter 'metaURL' for JuiceFS")
	}

	return nil
}

// ParseMountOptions parses JuiceFS-specific mount options
func (j *JuiceFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	options, err := j.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	options.MetaURL = parameters["metaURL"]
	if bucket, exists := parameters["bucket"]; exists {
		options.Bucket = bucket
	}

	return options, nil
}

// GenerateMountPodSpec generates pod spec for JuiceFS mounting
func (j *JuiceFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := j.GeneratePodName(volumeCtx.VolumeID)
	namespace := j.GetMountNamespace()

	mountCmd := fmt.Sprintf("mkdir -p %s && juicefs mount %s %s", targetPath, volumeCtx.MountOptions.MetaURL, targetPath)

	return &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     "juicedata/juicefs-csi-driver:latest",
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
		},
	}, nil
}

// Implement other required methods for JuiceFSAdapter
func (j *JuiceFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error {
	return nil
}

func (j *JuiceFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	// Similar implementation to NFS adapter
	return &MountResult{Success: true, MountPath: targetPath}, nil
}

func (j *JuiceFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	return nil
}

func (j *JuiceFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	return nil
}

func (j *JuiceFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	return nil
}

func (j *JuiceFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	return map[string]string{"filesystem": "juicefs", "mountPath": mountPath}, nil
}
