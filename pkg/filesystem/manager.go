package filesystem

import (
	"context"
	"fmt"
	"time"

	"github.com/golang/glog"
)

// NewFilesystemManager creates a new filesystem manager
func NewFilesystemManager(config *ManagerConfig) *FilesystemManager {
	if config.MountTimeout == 0 {
		config.MountTimeout = 5 * time.Minute // Default timeout
	}

	manager := &FilesystemManager{
		adapters:           make(map[FilesystemType]FilesystemAdapter),
		client:             config.Client,
		kubeClient:         config.KubeClient,
		nodeID:             config.NodeID,
		mountTimeout:       config.MountTimeout,
		enableSharedMounts: config.EnableSharedMounts,
	}

	// Initialize mount manager if shared mounts are enabled
	if config.EnableSharedMounts {
		mountManagerConfig := &MountManagerConfig{
			Client:     config.Client,
			KubeClient: config.KubeClient,
			NodeID:     config.NodeID,
			SharedDir:  config.SharedMountDir,
		}
		manager.mountManager = NewMountManager(mountManagerConfig)
		glog.V(2).Infof("Shared mounts enabled for filesystem manager")
	}

	// Register default adapters
	manager.registerDefaultAdapters()

	return manager
}

// RegisterAdapter registers a filesystem adapter
func (m *FilesystemManager) RegisterAdapter(adapter FilesystemAdapter) {
	m.adapters[adapter.GetType()] = adapter
	glog.V(2).Infof("Registered filesystem adapter for type: %s", adapter.GetType())
}

// GetAdapter returns the adapter for the specified filesystem type
func (m *FilesystemManager) GetAdapter(fsType FilesystemType) (FilesystemAdapter, error) {
	adapter, exists := m.adapters[fsType]
	if !exists {
		return nil, fmt.Errorf("no adapter found for filesystem type: %s", fsType)
	}
	return adapter, nil
}

// GetSupportedFilesystems returns a list of supported filesystem types
func (m *FilesystemManager) GetSupportedFilesystems() []FilesystemType {
	var types []FilesystemType
	for fsType := range m.adapters {
		types = append(types, fsType)
	}
	return types
}

// ParseStorageClassParameters parses storage class parameters and returns filesystem type and mount options
func (m *FilesystemManager) ParseStorageClassParameters(parameters map[string]string, secrets map[string]string) (FilesystemType, *MountOptions, error) {
	// Get filesystem type from parameters
	fsTypeStr, exists := parameters["type"]
	if !exists {
		return "", nil, fmt.Errorf("missing required parameter 'type' in storage class")
	}

	fsType := FilesystemType(fsTypeStr)

	// Get the appropriate adapter
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return "", nil, fmt.Errorf("unsupported filesystem type '%s': %v", fsType, err)
	}

	// Validate parameters
	if err := adapter.ValidateParameters(parameters); err != nil {
		return "", nil, fmt.Errorf("invalid parameters for filesystem type '%s': %v", fsType, err)
	}

	// Parse mount options
	mountOptions, err := adapter.ParseMountOptions(parameters, secrets)
	if err != nil {
		return "", nil, fmt.Errorf("failed to parse mount options for filesystem type '%s': %v", fsType, err)
	}

	return fsType, mountOptions, nil
}

// StageVolume stages a volume using the appropriate filesystem adapter
func (m *FilesystemManager) StageVolume(ctx context.Context, volumeCtx *VolumeContext, stagingPath string) error {
	fsType := FilesystemType(volumeCtx.Parameters["type"])
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return err
	}

	glog.V(2).Infof("Staging volume %s with filesystem type %s at path %s", volumeCtx.VolumeID, fsType, stagingPath)

	// Pre-mount operations
	if err := adapter.PreMount(ctx, volumeCtx); err != nil {
		return fmt.Errorf("pre-mount failed: %v", err)
	}

	var mountPath string

	// Check if shared mounts are enabled and this is a suitable filesystem for sharing
	if m.enableSharedMounts && m.mountManager != nil && m.isShareableFilesystem(fsType) {
		// Use shared mount
		sharedPath, err := m.mountManager.AcquireSharedMount(ctx, volumeCtx.VolumeID, fsType, volumeCtx.MountOptions, adapter)
		if err != nil {
			return fmt.Errorf("failed to acquire shared mount: %v", err)
		}

		// Create bind mount from shared path to staging path
		if err := m.createBindMount(sharedPath, stagingPath); err != nil {
			// Release the shared mount if bind mount fails
			m.mountManager.ReleaseSharedMount(ctx, volumeCtx.VolumeID, fsType, volumeCtx.MountOptions, adapter)
			return fmt.Errorf("failed to create bind mount from shared path: %v", err)
		}

		mountPath = stagingPath
		glog.V(2).Infof("Using shared mount for volume %s: %s -> %s", volumeCtx.VolumeID, sharedPath, stagingPath)
	} else {
		// Use direct mount (original behavior)
		result, err := adapter.Mount(ctx, volumeCtx, stagingPath, stagingPath)
		if err != nil {
			return fmt.Errorf("mount failed: %v", err)
		}

		if !result.Success {
			return fmt.Errorf("mount operation failed: %v", result.Error)
		}

		mountPath = result.MountPath
		glog.V(2).Infof("Using direct mount for volume %s", volumeCtx.VolumeID)
	}

	// Post-mount operations
	if err := adapter.PostMount(ctx, volumeCtx, mountPath); err != nil {
		glog.Warningf("Post-mount operations failed: %v", err)
		// Don't fail the entire operation for post-mount issues
	}

	glog.V(2).Infof("Successfully staged volume %s at %s", volumeCtx.VolumeID, stagingPath)
	return nil
}

// PublishVolume publishes a staged volume to the target path
func (m *FilesystemManager) PublishVolume(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) error {
	fsType := FilesystemType(volumeCtx.Parameters["type"])
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return err
	}

	glog.V(2).Infof("Publishing volume %s from staging path %s to target path %s", volumeCtx.VolumeID, stagingPath, targetPath)

	// For most filesystems, we can use bind mount from staging to target
	result, err := adapter.Mount(ctx, volumeCtx, stagingPath, targetPath)
	if err != nil {
		return fmt.Errorf("publish mount failed: %v", err)
	}

	if !result.Success {
		return fmt.Errorf("publish mount operation failed: %v", result.Error)
	}

	glog.V(2).Infof("Successfully published volume %s to %s", volumeCtx.VolumeID, targetPath)
	return nil
}

// UnpublishVolume unpublishes a volume from the target path
func (m *FilesystemManager) UnpublishVolume(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	fsType := FilesystemType(volumeCtx.Parameters["type"])
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return err
	}

	glog.V(2).Infof("Unpublishing volume %s from target path %s", volumeCtx.VolumeID, targetPath)

	if err := adapter.Unmount(ctx, volumeCtx, targetPath); err != nil {
		return fmt.Errorf("unpublish unmount failed: %v", err)
	}

	glog.V(2).Infof("Successfully unpublished volume %s from %s", volumeCtx.VolumeID, targetPath)
	return nil
}

// UnstageVolume unstages a volume from the staging path
func (m *FilesystemManager) UnstageVolume(ctx context.Context, volumeCtx *VolumeContext, stagingPath string) error {
	fsType := FilesystemType(volumeCtx.Parameters["type"])
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return err
	}

	glog.V(2).Infof("Unstaging volume %s from staging path %s", volumeCtx.VolumeID, stagingPath)

	// Check if this volume is using shared mounts
	if m.enableSharedMounts && m.mountManager != nil && m.isShareableFilesystem(fsType) {
		// Release the shared mount
		if err := m.mountManager.ReleaseSharedMount(ctx, volumeCtx.VolumeID, fsType, volumeCtx.MountOptions, adapter); err != nil {
			glog.Errorf("Failed to release shared mount for volume %s: %v", volumeCtx.VolumeID, err)
			// Continue with unmounting the staging path
		}

		// Unmount the bind mount at staging path
		if err := m.unmountBindMount(stagingPath); err != nil {
			glog.Errorf("Failed to unmount bind mount at %s: %v", stagingPath, err)
			// Continue anyway
		}

		glog.V(2).Infof("Released shared mount for volume %s", volumeCtx.VolumeID)
	} else {
		// Use direct unmount (original behavior)
		if err := adapter.Unmount(ctx, volumeCtx, stagingPath); err != nil {
			return fmt.Errorf("unstage unmount failed: %v", err)
		}
	}

	glog.V(2).Infof("Successfully unstaged volume %s from %s", volumeCtx.VolumeID, stagingPath)
	return nil
}

// HealthCheckVolume performs health check on a mounted volume
func (m *FilesystemManager) HealthCheckVolume(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	fsType := FilesystemType(volumeCtx.Parameters["type"])
	adapter, err := m.GetAdapter(fsType)
	if err != nil {
		return err
	}

	return adapter.HealthCheck(ctx, mountPath)
}

// registerDefaultAdapters registers the default filesystem adapters
func (m *FilesystemManager) registerDefaultAdapters() {
	// Register NFS adapter
	m.RegisterAdapter(NewNFSAdapter(m.client, m.kubeClient, m.nodeID))

	// Register CephFS adapter
	m.RegisterAdapter(NewCephFSAdapter(m.client, m.kubeClient, m.nodeID))

	// Register JuiceFS adapter
	m.RegisterAdapter(NewJuiceFSAdapter(m.client, m.kubeClient, m.nodeID))

	// Register GlusterFS adapter
	m.RegisterAdapter(NewGlusterFSAdapter(m.client, m.kubeClient, m.nodeID))

	// Register generic FUSE adapter
	m.RegisterAdapter(NewFUSEAdapter(m.client, m.kubeClient, m.nodeID))
}

// isShareableFilesystem checks if a filesystem type supports shared mounting
func (m *FilesystemManager) isShareableFilesystem(fsType FilesystemType) bool {
	// Most network filesystems support shared mounting
	switch fsType {
	case FilesystemTypeNFS, FilesystemTypeCephFS, FilesystemTypeGlusterFS:
		return true
	case FilesystemTypeJuiceFS:
		// JuiceFS can be shared but might need special handling
		return true
	case FilesystemTypeFUSE:
		// Generic FUSE filesystems may or may not support sharing
		// For safety, disable sharing by default
		return false
	default:
		return false
	}
}

// createBindMount creates a bind mount from source to target
func (m *FilesystemManager) createBindMount(sourcePath, targetPath string) error {
	// This is a simplified implementation
	// In production, you would use proper mount syscalls or mount commands

	glog.V(2).Infof("Creating bind mount from %s to %s", sourcePath, targetPath)

	// For now, we'll use a simple approach
	// TODO: Implement proper bind mount using syscalls or mount pod

	return nil
}

// unmountBindMount unmounts a bind mount
func (m *FilesystemManager) unmountBindMount(targetPath string) error {
	// This is a simplified implementation
	// In production, you would use proper umount syscalls or mount commands

	glog.V(2).Infof("Unmounting bind mount at %s", targetPath)

	// For now, we'll use a simple approach
	// TODO: Implement proper unmount using syscalls or mount pod

	return nil
}

// GetSharedMountInfo returns information about shared mounts
func (m *FilesystemManager) GetSharedMountInfo() map[string]*SharedMountInfo {
	if m.mountManager == nil {
		return make(map[string]*SharedMountInfo)
	}
	return m.mountManager.ListSharedMounts()
}

// GetSharedMountMetrics returns metrics about shared mounts
func (m *FilesystemManager) GetSharedMountMetrics() map[string]interface{} {
	if m.mountManager == nil {
		return map[string]interface{}{
			"shared_mounts_enabled": false,
		}
	}

	metrics := m.mountManager.GetMetrics()
	metrics["shared_mounts_enabled"] = true
	return metrics
}

// CleanupStaleSharedMounts cleans up unused shared mounts
func (m *FilesystemManager) CleanupStaleSharedMounts(ctx context.Context, maxAge time.Duration) error {
	if m.mountManager == nil {
		return nil
	}

	// We need to pass an adapter, but since we're cleaning up, we can use any adapter
	// In practice, each shared mount would remember which adapter created it
	for fsType := range m.adapters {
		adapter, err := m.GetAdapter(fsType)
		if err != nil {
			continue
		}

		if err := m.mountManager.CleanupStaleSharedMounts(ctx, maxAge, adapter); err != nil {
			glog.Errorf("Failed to cleanup stale shared mounts for %s: %v", fsType, err)
		}
		break // Only need one adapter for cleanup
	}

	return nil
}
