package filesystem

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/golang/glog"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// NFSAdapter implements FilesystemAdapter for NFS
type NFSAdapter struct {
	*BaseAdapter
}

// NewNFSAdapter creates a new NFS adapter
func NewNFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &NFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeNFS),
	}
}

// ValidateParameters validates NFS-specific parameters
func (n *NFSAdapter) ValidateParameters(parameters map[string]string) error {
	// Validate common parameters first
	if err := n.ValidateCommonParameters(parameters); err != nil {
		return err
	}

	// Validate NFS-specific parameters
	server, exists := parameters["server"]
	if !exists || server == "" {
		return fmt.Errorf("missing required parameter 'server' for NFS")
	}

	path, exists := parameters["path"]
	if !exists || path == "" {
		return fmt.Errorf("missing required parameter 'path' for NFS")
	}

	// Validate NFS version if specified
	if version, exists := parameters["nfsVersion"]; exists {
		validVersions := []string{"3", "4", "4.0", "4.1", "4.2"}
		valid := false
		for _, v := range validVersions {
			if version == v {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid NFS version '%s', supported versions: %v", version, validVersions)
		}
	}

	return nil
}

// ParseMountOptions parses NFS-specific mount options
func (n *NFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	// Parse common options first
	options, err := n.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}

	// Parse NFS-specific options
	options.Server = parameters["server"]
	options.Path = parameters["path"]

	if version, exists := parameters["nfsVersion"]; exists {
		options.NFSVersion = version
	} else {
		options.NFSVersion = "4" // Default to NFSv4
	}

	// Parse additional mount options
	if opts, exists := parameters["mountOptions"]; exists {
		options.MountFlags = append(options.MountFlags, strings.Split(opts, ",")...)
	}

	return options, nil
}

// GenerateMountPodSpec generates pod spec for NFS mounting
func (n *NFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := n.GeneratePodName(volumeCtx.VolumeID)
	namespace := n.GetMountNamespace()

	// Build mount command
	mountCmd := n.buildMountCommand(volumeCtx.MountOptions, stagingPath)

	spec := &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     n.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		Env: []v1.EnvVar{
			{
				Name:  "MOUNT_TYPE",
				Value: "nfs",
			},
			{
				Name:  "MOUNT_PATH",
				Value: stagingPath,
			},
		},
		VolumeMounts: []v1.VolumeMount{
			{
				Name:             "host-mount",
				MountPath:        "/host",
				MountPropagation: &[]v1.MountPropagationMode{v1.MountPropagationBidirectional}[0],
			},
		},
		Volumes: []v1.Volume{
			{
				Name: "host-mount",
				VolumeSource: v1.VolumeSource{
					HostPath: &v1.HostPathVolumeSource{
						Path: "/",
						Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
					},
				},
			},
		},
		Resources: v1.ResourceRequirements{
			Requests: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("100m"),
				v1.ResourceMemory: resource.MustParse("128Mi"),
			},
			Limits: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("500m"),
				v1.ResourceMemory: resource.MustParse("512Mi"),
			},
		},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}

	return spec, nil
}

// PreMount performs pre-mount operations for NFS
func (n *NFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error {
	glog.V(2).Infof("Performing NFS pre-mount operations for volume %s", volumeCtx.VolumeID)

	// Check if NFS server is reachable (optional, can be implemented later)
	// For now, we'll just log the operation
	glog.V(2).Infof("NFS server: %s, path: %s", volumeCtx.MountOptions.Server, volumeCtx.MountOptions.Path)

	return nil
}

// Mount performs NFS mount operation using mount pod
func (n *NFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	glog.V(2).Infof("Mounting NFS volume %s from %s to %s", volumeCtx.VolumeID, stagingPath, targetPath)

	// Ensure target directory exists
	if err := n.EnsureDirectory(targetPath); err != nil {
		return nil, fmt.Errorf("failed to create target directory: %v", err)
	}

	// If this is a bind mount from staging to target, use simple bind mount
	if stagingPath != targetPath {
		return n.bindMount(stagingPath, targetPath)
	}

	// Generate mount pod spec
	spec, err := n.GenerateMountPodSpec(ctx, volumeCtx, stagingPath, targetPath)
	if err != nil {
		return nil, fmt.Errorf("failed to generate mount pod spec: %v", err)
	}

	// Create and wait for mount pod
	if err := n.CreateMountPod(ctx, spec); err != nil {
		return nil, fmt.Errorf("failed to create mount pod: %v", err)
	}

	// Wait for mount pod to complete
	if err := n.WaitForMountPod(ctx, spec.Namespace, spec.Name, 5*time.Minute); err != nil {
		// Clean up failed pod
		n.DeleteMountPod(ctx, spec.Namespace, spec.Name)
		return nil, fmt.Errorf("mount pod failed: %v", err)
	}

	// Clean up successful pod
	if err := n.DeleteMountPod(ctx, spec.Namespace, spec.Name); err != nil {
		glog.Warningf("Failed to clean up mount pod: %v", err)
	}

	return &MountResult{
		Success:      true,
		MountPath:    targetPath,
		MountPodName: spec.Name,
	}, nil
}

// Unmount performs NFS unmount operation
func (n *NFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	glog.V(2).Infof("Unmounting NFS volume %s from %s", volumeCtx.VolumeID, targetPath)

	// Generate unmount pod spec
	podName := n.GeneratePodName(volumeCtx.VolumeID) + "-unmount"
	namespace := n.GetMountNamespace()

	// Build unmount command for host filesystem
	hostTargetPath := filepath.Join("/host", targetPath)
	unmountCmd := fmt.Sprintf("umount %s || true && echo 'Unmount completed'", hostTargetPath)

	spec := &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     n.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", unmountCmd},
		VolumeMounts: []v1.VolumeMount{
			{
				Name:             "host-mount",
				MountPath:        "/host",
				MountPropagation: &[]v1.MountPropagationMode{v1.MountPropagationBidirectional}[0],
			},
		},
		Volumes: []v1.Volume{
			{
				Name: "host-mount",
				VolumeSource: v1.VolumeSource{
					HostPath: &v1.HostPathVolumeSource{
						Path: "/",
						Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
					},
				},
			},
		},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}

	// Create and wait for unmount pod
	if err := n.CreateMountPod(ctx, spec); err != nil {
		return fmt.Errorf("failed to create unmount pod: %v", err)
	}

	// Wait for unmount pod to complete
	if err := n.WaitForMountPod(ctx, spec.Namespace, spec.Name, 2*time.Minute); err != nil {
		glog.Warningf("Unmount pod failed: %v", err)
		// Continue with cleanup even if unmount failed
	}

	// Clean up unmount pod
	if err := n.DeleteMountPod(ctx, spec.Namespace, spec.Name); err != nil {
		glog.Warningf("Failed to clean up unmount pod: %v", err)
	}

	return nil
}

// PostMount performs post-mount operations for NFS
func (n *NFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	glog.V(2).Infof("Performing NFS post-mount operations for volume %s at %s", volumeCtx.VolumeID, mountPath)

	// Set proper permissions if needed
	// This is optional and can be configured based on requirements

	return nil
}

// HealthCheck checks NFS mount health
func (n *NFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	// Check if mount path exists and is accessible
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}

	// Try to read the mount point
	if _, err := os.ReadDir(mountPath); err != nil {
		return fmt.Errorf("cannot read mount directory: %v", err)
	}

	return nil
}

// GetMountInfo returns NFS mount information
func (n *NFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	info := map[string]string{
		"filesystem": "nfs",
		"mountPath":  mountPath,
	}

	// Add more mount information if needed
	return info, nil
}

// buildMountCommand builds the NFS mount command
func (n *NFSAdapter) buildMountCommand(options *MountOptions, targetPath string) string {
	var cmd strings.Builder

	// Create mount directory in host filesystem
	hostTargetPath := filepath.Join("/host", targetPath)
	cmd.WriteString(fmt.Sprintf("mkdir -p %s && ", hostTargetPath))

	// Build mount command
	cmd.WriteString("mount -t nfs")

	// Build mount options
	var mountOpts []string

	// Add NFS version
	if options.NFSVersion != "" {
		mountOpts = append(mountOpts, fmt.Sprintf("vers=%s", options.NFSVersion))
	}

	// Add custom mount flags
	mountOpts = append(mountOpts, options.MountFlags...)

	// Add read-only flag
	if options.ReadOnly {
		mountOpts = append(mountOpts, "ro")
	}

	// Add default NFS options for better reliability
	mountOpts = append(mountOpts, "hard", "intr", "timeo=600", "retrans=2")

	if len(mountOpts) > 0 {
		cmd.WriteString(fmt.Sprintf(" -o %s", strings.Join(mountOpts, ",")))
	}

	// Add server and path
	cmd.WriteString(fmt.Sprintf(" %s:%s %s", options.Server, options.Path, hostTargetPath))

	// Verify mount was successful
	cmd.WriteString(fmt.Sprintf(" && echo 'Mount successful' && ls -la %s", hostTargetPath))

	return cmd.String()
}

// bindMount performs a bind mount operation using a mount pod
func (n *NFSAdapter) bindMount(sourcePath, targetPath string) (*MountResult, error) {
	// Ensure target directory exists
	if err := n.EnsureDirectory(targetPath); err != nil {
		return nil, fmt.Errorf("failed to create target directory: %v", err)
	}

	// Create a mount pod for bind mount operation
	podName := fmt.Sprintf("bind-mount-%d", time.Now().Unix())
	namespace := n.GetMountNamespace()

	// Build bind mount command
	hostSourcePath := filepath.Join("/host", sourcePath)
	hostTargetPath := filepath.Join("/host", targetPath)
	bindCmd := fmt.Sprintf("mount --bind %s %s && echo 'Bind mount successful'", hostSourcePath, hostTargetPath)

	spec := &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     n.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", bindCmd},
		VolumeMounts: []v1.VolumeMount{
			{
				Name:             "host-mount",
				MountPath:        "/host",
				MountPropagation: &[]v1.MountPropagationMode{v1.MountPropagationBidirectional}[0],
			},
		},
		Volumes: []v1.Volume{
			{
				Name: "host-mount",
				VolumeSource: v1.VolumeSource{
					HostPath: &v1.HostPathVolumeSource{
						Path: "/",
						Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
					},
				},
			},
		},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}

	// Create and wait for bind mount pod
	ctx := context.Background()
	if err := n.CreateMountPod(ctx, spec); err != nil {
		return nil, fmt.Errorf("failed to create bind mount pod: %v", err)
	}

	// Wait for bind mount pod to complete
	if err := n.WaitForMountPod(ctx, spec.Namespace, spec.Name, 2*time.Minute); err != nil {
		// Clean up failed pod
		n.DeleteMountPod(ctx, spec.Namespace, spec.Name)
		return nil, fmt.Errorf("bind mount pod failed: %v", err)
	}

	// Clean up successful pod
	if err := n.DeleteMountPod(ctx, spec.Namespace, spec.Name); err != nil {
		glog.Warningf("Failed to clean up bind mount pod: %v", err)
	}

	return &MountResult{
		Success:      true,
		MountPath:    targetPath,
		MountPodName: spec.Name,
	}, nil
}
