package filesystem

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/golang/glog"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// CephFSAdapter implements FilesystemAdapter for CephFS
type CephFSAdapter struct {
	*BaseAdapter
}

// NewCephFSAdapter creates a new CephFS adapter
func NewCephFSAdapter(client client.Client, kubeClient kubernetes.Interface, nodeID string) FilesystemAdapter {
	return &CephFSAdapter{
		BaseAdapter: NewBaseAdapter(client, kubeClient, nodeID, FilesystemTypeCephFS),
	}
}

// ValidateParameters validates CephFS-specific parameters
func (c *CephFSAdapter) ValidateParameters(parameters map[string]string) error {
	// Validate common parameters first
	if err := c.ValidateCommonParameters(parameters); err != nil {
		return err
	}
	
	// Validate CephFS-specific parameters
	monitors, exists := parameters["monitors"]
	if !exists || monitors == "" {
		return fmt.Errorf("missing required parameter 'monitors' for CephFS")
	}
	
	// Validate monitors format (comma-separated list of monitor addresses)
	monitorList := strings.Split(monitors, ",")
	for _, monitor := range monitorList {
		if strings.TrimSpace(monitor) == "" {
			return fmt.Errorf("invalid monitor address in monitors list")
		}
	}
	
	return nil
}

// ParseMountOptions parses CephFS-specific mount options
func (c *CephFSAdapter) ParseMountOptions(parameters map[string]string, secrets map[string]string) (*MountOptions, error) {
	// Parse common options first
	options, err := c.ParseCommonParameters(parameters)
	if err != nil {
		return nil, err
	}
	
	// Parse CephFS-specific options
	options.Monitors = strings.Split(parameters["monitors"], ",")
	
	if user, exists := parameters["user"]; exists {
		options.User = user
	} else {
		options.User = "admin" // Default user
	}
	
	if secretRef, exists := parameters["secretRef"]; exists {
		options.SecretRef = secretRef
	}
	
	// Parse root path
	if rootPath, exists := parameters["rootPath"]; exists {
		options.Path = rootPath
	} else {
		options.Path = "/" // Default to root
	}
	
	return options, nil
}

// GenerateMountPodSpec generates pod spec for CephFS mounting
func (c *CephFSAdapter) GenerateMountPodSpec(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountPodSpec, error) {
	podName := c.GeneratePodName(volumeCtx.VolumeID)
	namespace := c.GetMountNamespace()
	
	// Build mount command
	mountCmd := c.buildMountCommand(volumeCtx.MountOptions, stagingPath)
	
	env := []v1.EnvVar{
		{
			Name:  "MOUNT_TYPE",
			Value: "cephfs",
		},
		{
			Name:  "MOUNT_PATH",
			Value: stagingPath,
		},
	}
	
	// Add secret if specified
	if volumeCtx.MountOptions.SecretRef != "" {
		env = append(env, v1.EnvVar{
			Name: "CEPH_SECRET",
			ValueFrom: &v1.EnvVarSource{
				SecretKeyRef: &v1.SecretKeySelector{
					LocalObjectReference: v1.LocalObjectReference{
						Name: volumeCtx.MountOptions.SecretRef,
					},
					Key: "key",
				},
			},
		})
	}
	
	spec := &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     c.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", mountCmd},
		Env:       env,
		VolumeMounts: []v1.VolumeMount{
			{
				Name:             "host-mount",
				MountPath:        "/host",
				MountPropagation: &[]v1.MountPropagationMode{v1.MountPropagationBidirectional}[0],
			},
		},
		Volumes: []v1.Volume{
			{
				Name: "host-mount",
				VolumeSource: v1.VolumeSource{
					HostPath: &v1.HostPathVolumeSource{
						Path: "/",
						Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
					},
				},
			},
		},
		Resources: v1.ResourceRequirements{
			Requests: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("100m"),
				v1.ResourceMemory: resource.MustParse("128Mi"),
			},
			Limits: v1.ResourceList{
				v1.ResourceCPU:    resource.MustParse("500m"),
				v1.ResourceMemory: resource.MustParse("512Mi"),
			},
		},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}
	
	return spec, nil
}

// PreMount performs pre-mount operations for CephFS
func (c *CephFSAdapter) PreMount(ctx context.Context, volumeCtx *VolumeContext) error {
	glog.V(2).Infof("Performing CephFS pre-mount operations for volume %s", volumeCtx.VolumeID)
	
	// Check if monitors are reachable (optional, can be implemented later)
	glog.V(2).Infof("CephFS monitors: %v, user: %s", volumeCtx.MountOptions.Monitors, volumeCtx.MountOptions.User)
	
	return nil
}

// Mount performs CephFS mount operation using mount pod
func (c *CephFSAdapter) Mount(ctx context.Context, volumeCtx *VolumeContext, stagingPath, targetPath string) (*MountResult, error) {
	glog.V(2).Infof("Mounting CephFS volume %s from %s to %s", volumeCtx.VolumeID, stagingPath, targetPath)
	
	// Ensure target directory exists
	if err := c.EnsureDirectory(targetPath); err != nil {
		return nil, fmt.Errorf("failed to create target directory: %v", err)
	}
	
	// If this is a bind mount from staging to target, use simple bind mount
	if stagingPath != targetPath {
		return c.bindMount(stagingPath, targetPath)
	}
	
	// Generate mount pod spec
	spec, err := c.GenerateMountPodSpec(ctx, volumeCtx, stagingPath, targetPath)
	if err != nil {
		return nil, fmt.Errorf("failed to generate mount pod spec: %v", err)
	}
	
	// Create and wait for mount pod
	if err := c.CreateMountPod(ctx, spec); err != nil {
		return nil, fmt.Errorf("failed to create mount pod: %v", err)
	}
	
	// Wait for mount pod to complete
	if err := c.WaitForMountPod(ctx, spec.Namespace, spec.Name, 5*time.Minute); err != nil {
		// Clean up failed pod
		c.DeleteMountPod(ctx, spec.Namespace, spec.Name)
		return nil, fmt.Errorf("mount pod failed: %v", err)
	}
	
	// Clean up successful pod
	if err := c.DeleteMountPod(ctx, spec.Namespace, spec.Name); err != nil {
		glog.Warningf("Failed to clean up mount pod: %v", err)
	}
	
	return &MountResult{
		Success:      true,
		MountPath:    targetPath,
		MountPodName: spec.Name,
	}, nil
}

// Unmount performs CephFS unmount operation
func (c *CephFSAdapter) Unmount(ctx context.Context, volumeCtx *VolumeContext, targetPath string) error {
	glog.V(2).Infof("Unmounting CephFS volume %s from %s", volumeCtx.VolumeID, targetPath)
	
	// Generate unmount pod spec
	podName := c.GeneratePodName(volumeCtx.VolumeID) + "-unmount"
	namespace := c.GetMountNamespace()
	
	unmountCmd := fmt.Sprintf("umount %s || true", targetPath)
	
	spec := &MountPodSpec{
		Name:      podName,
		Namespace: namespace,
		Image:     c.GetDefaultMountImage(),
		Command:   []string{"/bin/sh"},
		Args:      []string{"-c", unmountCmd},
		VolumeMounts: []v1.VolumeMount{
			{
				Name:             "host-mount",
				MountPath:        "/host",
				MountPropagation: &[]v1.MountPropagationMode{v1.MountPropagationBidirectional}[0],
			},
		},
		Volumes: []v1.Volume{
			{
				Name: "host-mount",
				VolumeSource: v1.VolumeSource{
					HostPath: &v1.HostPathVolumeSource{
						Path: "/",
						Type: &[]v1.HostPathType{v1.HostPathDirectory}[0],
					},
				},
			},
		},
		SecurityContext: &v1.SecurityContext{
			Privileged: &[]bool{true}[0],
			Capabilities: &v1.Capabilities{
				Add: []v1.Capability{"SYS_ADMIN"},
			},
		},
	}
	
	// Create and wait for unmount pod
	if err := c.CreateMountPod(ctx, spec); err != nil {
		return fmt.Errorf("failed to create unmount pod: %v", err)
	}
	
	// Wait for unmount pod to complete
	if err := c.WaitForMountPod(ctx, spec.Namespace, spec.Name, 2*time.Minute); err != nil {
		glog.Warningf("Unmount pod failed: %v", err)
	}
	
	// Clean up unmount pod
	if err := c.DeleteMountPod(ctx, spec.Namespace, spec.Name); err != nil {
		glog.Warningf("Failed to clean up unmount pod: %v", err)
	}
	
	return nil
}

// PostMount performs post-mount operations for CephFS
func (c *CephFSAdapter) PostMount(ctx context.Context, volumeCtx *VolumeContext, mountPath string) error {
	glog.V(2).Infof("Performing CephFS post-mount operations for volume %s at %s", volumeCtx.VolumeID, mountPath)
	return nil
}

// HealthCheck checks CephFS mount health
func (c *CephFSAdapter) HealthCheck(ctx context.Context, mountPath string) error {
	// Check if mount path exists and is accessible
	if _, err := os.Stat(mountPath); err != nil {
		return fmt.Errorf("mount path not accessible: %v", err)
	}
	
	// Try to read the mount point
	if _, err := os.ReadDir(mountPath); err != nil {
		return fmt.Errorf("cannot read mount directory: %v", err)
	}
	
	return nil
}

// GetMountInfo returns CephFS mount information
func (c *CephFSAdapter) GetMountInfo(ctx context.Context, mountPath string) (map[string]string, error) {
	info := map[string]string{
		"filesystem": "cephfs",
		"mountPath":  mountPath,
	}
	return info, nil
}

// buildMountCommand builds the CephFS mount command
func (c *CephFSAdapter) buildMountCommand(options *MountOptions, targetPath string) string {
	var cmd strings.Builder
	
	// Create mount directory
	cmd.WriteString(fmt.Sprintf("mkdir -p %s && ", targetPath))
	
	// Build mount command
	cmd.WriteString("mount -t ceph")
	
	// Add monitors
	monitorStr := strings.Join(options.Monitors, ",")
	cmd.WriteString(fmt.Sprintf(" %s:%s", monitorStr, options.Path))
	
	// Add mount options
	var mountOpts []string
	
	if options.User != "" {
		mountOpts = append(mountOpts, fmt.Sprintf("name=%s", options.User))
	}
	
	if options.ReadOnly {
		mountOpts = append(mountOpts, "ro")
	}
	
	// Add custom mount flags
	mountOpts = append(mountOpts, options.MountFlags...)
	
	if len(mountOpts) > 0 {
		cmd.WriteString(fmt.Sprintf(" -o %s", strings.Join(mountOpts, ",")))
	}
	
	cmd.WriteString(fmt.Sprintf(" %s", targetPath))
	
	return cmd.String()
}

// bindMount performs a bind mount operation
func (c *CephFSAdapter) bindMount(sourcePath, targetPath string) (*MountResult, error) {
	// Ensure target directory exists
	if err := c.EnsureDirectory(targetPath); err != nil {
		return nil, fmt.Errorf("failed to create target directory: %v", err)
	}
	
	return &MountResult{
		Success:   true,
		MountPath: targetPath,
	}, nil
}
