package pkg

import (
	"context"
	"sync"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"google.golang.org/grpc"
)

func newNonBlockingGRPCServer() *nonBlockingGRPCServer {
	return &nonBlockingGRPCServer{}
}

// nonBlockingGRPCServer
type nonBlockingGRPCServer struct {
	wg      sync.WaitGroup
	server  *grpc.Server
	cleanup func()
}

func (s *nonBlockingGRPCServer) start(endpoint string, ids csi.IdentityServer, cs csi.ControllerServer, ns csi.NodeServer, gcs csi.GroupControllerServer, sms csi.SnapshotMetadataServer) {
	s.wg.Add(1)
	go s.serve(endpoint, ids, cs, ns, gcs, sms)
}

func (s *nonBlockingGRPCServer) wait() {
	s.wg.Wait()
}

// func (s *nonBlockingGRPCServer) Stop() {
// 	s.server.GracefulStop()
// 	s.cleanup()
// }

// func (s *nonBlockingGRPCServer) ForceStop() {
// 	s.server.Stop()
// 	s.cleanup()
// }

func (s *nonBlockingGRPCServer) serve(ep string, ids csi.IdentityServer, cs csi.ControllerServer, ns csi.NodeServer, gcs csi.GroupControllerServer, sms csi.SnapshotMetadataServer) {
	listener, cleanup, err := listen(ep)
	if err != nil {
		glog.Fatalf("Failed to listen on endpoint %s: %v", ep, err)
	}

	opts := []grpc.ServerOption{
		grpc.UnaryInterceptor(logGRPC),
	}
	server := grpc.NewServer(opts...)
	s.server = server
	s.cleanup = cleanup

	if ids != nil {
		csi.RegisterIdentityServer(server, ids)
	}
	if cs != nil {
		csi.RegisterControllerServer(server, cs)
	}
	if ns != nil {
		csi.RegisterNodeServer(server, ns)
	}
	if gcs != nil {
		csi.RegisterGroupControllerServer(server, gcs)
	}
	if sms != nil {
		csi.RegisterSnapshotMetadataServer(server, sms)
	}
	glog.V(5).Infof("Listening for connections on address: %#v", listener.Addr())
	server.Serve(listener)
}

func logGRPC(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
	pri := glog.Level(3)
	if info.FullMethod == "/csi.v1.Identity/Probe" {
		// This call occurs frequently, therefore it only gets log at level 5.
		pri = 5
	}
	glog.V(pri).Infof("GRPC call: %s", info.FullMethod)
	resp, err := handler(ctx, req)
	if err != nil {
		// Always log errors. Probably not useful though without the method name?!
		glog.Errorf("GRPC error: %v", err)
	}
	return resp, err
}
