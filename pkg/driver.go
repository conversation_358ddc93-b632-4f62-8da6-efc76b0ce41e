package pkg

import (
	"context"
	"os"
	"path/filepath"

	"github.com/golang/glog"
	"github.com/kubegems/xpai-csi/pkg/filesystem"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/manager"
)

const (
	driverName = "fuse.csi.xiaoshiai.cn"
	version    = "1.0.0"
)

var _ manager.Runnable = &Driver{}

type Driver struct {
	client               client.Client
	apiReader            client.Reader
	nodeAuthorizedClient *kubernetes.Clientset
	nodeId, endpoint     string
	locks                *volumeLocks
}

// endpoint: unix:///var/lib/kubelet/csi-plugins/fuse.csi.xiaoshiai.cn/csi.sock
func NewDriver(nodeID, endpoint string, client client.Client, apiReader client.Reader, nodeAuthorizedClient *kubernetes.Clientset) *Driver {
	glog.V(5).Infof("Driver: %v version: %v", driverName, version)

	proto, addr := splitSchemaAddr(endpoint)
	glog.V(5).Infof("protocol: %v addr: %v", proto, addr)
	socketDir := filepath.Dir(addr)
	err := os.MkdirAll(socketDir, 0755)
	if err != nil {
		glog.Errorf("failed due to %v", err)
		os.Exit(1)
	}

	// csiDriver := csicommon.NewCSIDriver(driverName, version, nodeID)
	// csiDriver.AddControllerServiceCapabilities([]csi.ControllerServiceCapability_RPC_Type{csi.ControllerServiceCapability_RPC_CREATE_DELETE_VOLUME})
	// csiDriver.AddVolumeCapabilityAccessModes([]csi.VolumeCapability_AccessMode_Mode{csi.VolumeCapability_AccessMode_MULTI_NODE_MULTI_WRITER})

	return &Driver{
		nodeId:               nodeID,
		endpoint:             endpoint,
		client:               client,
		nodeAuthorizedClient: nodeAuthorizedClient,
		apiReader:            apiReader,
		locks:                newVolumeLocks(),
	}
}

// Start implements manager.Runnable.
func (d *Driver) Start(context.Context) error {
	s := newNonBlockingGRPCServer()
	// Initialize filesystem manager for controller
	fsManager := filesystem.NewFilesystemManager(&filesystem.ManagerConfig{
		Client:                      d.client,
		KubeClient:                  d.nodeAuthorizedClient,
		NodeID:                      d.nodeId,
		EnableSharedMounts:          true,
		EnableCrossNamespaceSharing: true,
		SharedMountDir:              "/var/lib/xpai-csi/shared",
	})

	s.start(
		d.endpoint,
		newIdentityServer(driverName, version),
		newControllerServer(d.client, d.nodeAuthorizedClient, fsManager),
		newNodeServer(d.nodeId, d.client, d.nodeAuthorizedClient),
		nil,
		nil,
	)
	s.wait()
	return nil
}
