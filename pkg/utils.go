package pkg

import (
	"fmt"
	"net"
	"os"
	"regexp"
)

// SplitSchemaAddr splits the address string into 2 parts: proto and addr.
// Proto is set to "tcp" in default.
// It returns proto and addr separately.
func splitSchemaAddr(addr string) (string, string) {
	parts := reSchema.FindStringSubmatch(addr)
	proto, addr := parts[1], parts[2]
	if proto == "" {
		proto = "tcp"
	}
	return proto, addr
}

var reSchema = regexp.MustCompile("^(?:([a-z0-9]+)://)?(.*)$")

func listen(endpoint string) (net.Listener, func(), error) {
	proto, addr := splitSchemaAddr(endpoint)
	cleanup := func() {}
	if proto == "unix" {
		if err := os.Remove(addr); err != nil && !os.IsNotExist(err) { //nolint: vetshadow
			return nil, nil, fmt.Errorf("%s: %q", addr, err)
		}
		cleanup = func() {
			os.Remove(addr)
		}
	}

	l, err := net.Listen(proto, addr)
	return l, cleanup, err
}
