package pkg

import (
	"sync"

	"k8s.io/apimachinery/pkg/util/sets"
)

type volumeLocks struct {
	locks sets.Set[string] // Using sets.Set[string] for better type safety
	mutex sync.Mutex
}

func newVolumeLocks() *volumeLocks {
	return &volumeLocks{
		locks: sets.New[string](),
	}
}

// TryAcquire tries to acquire the lock for operating on resourceID and returns true if successful.
// If another operation is already using resourceID, returns false.
func (lock *volumeLocks) TryAcquire(volumeID string) bool {
	lock.mutex.Lock()
	defer lock.mutex.Unlock()
	if lock.locks.Has(volumeID) {
		return false
	}
	lock.locks.Insert(volumeID)
	return true
}

// Release releases lock in volume level
func (lock *volumeLocks) Release(volumeID string) {
	lock.mutex.Lock()
	defer lock.mutex.Unlock()
	lock.locks.Delete(volumeID)
}
