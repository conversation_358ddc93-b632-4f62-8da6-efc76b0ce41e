package pkg

import (
	"context"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ csi.NodeServer = &nodeServer{}

type nodeServer struct {
	csi.UnimplementedNodeServer
	nodeID               string
	client               client.Client
	apiReader            client.Reader
	nodeAuthorizedClient *kubernetes.Clientset
	locks                *volumeLocks
	node                 *v1.Node
}

func newNodeServer(nodeID string) csi.NodeServer {
	return &nodeServer{
		nodeID: nodeID,
	}
}

// NodeExpandVolume implements csi.NodeServer.
func (n *nodeServer) NodeExpandVolume(ctx context.Context, req *csi.NodeExpandVolumeRequest) (*csi.NodeExpandVolumeResponse, error) {
	glog.V(5).Infof("NodeExpandVolume called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeExpandVolume is not implemented")
}

// NodeGetCapabilities implements csi.NodeServer.
func (n *nodeServer) NodeGetCapabilities(ctx context.Context, req *csi.NodeGetCapabilitiesRequest) (*csi.NodeGetCapabilitiesResponse, error) {
	glog.V(5).Infof("NodeGetCapabilities called with request: %s", req.String())
	return &csi.NodeGetCapabilitiesResponse{
		Capabilities: []*csi.NodeServiceCapability{
			{
				Type: &csi.NodeServiceCapability_Rpc{
					Rpc: &csi.NodeServiceCapability_RPC{
						Type: csi.NodeServiceCapability_RPC_STAGE_UNSTAGE_VOLUME,
					},
				},
			},
		},
	}, nil
}

// NodeGetInfo implements csi.NodeServer.
func (n *nodeServer) NodeGetInfo(ctx context.Context, req *csi.NodeGetInfoRequest) (*csi.NodeGetInfoResponse, error) {
	glog.V(5).Infof("NodeGetInfo called with request: %s", req.String())
	return &csi.NodeGetInfoResponse{
		NodeId: n.nodeID,
	}, nil
}

// NodeGetVolumeStats implements csi.NodeServer.
func (n *nodeServer) NodeGetVolumeStats(ctx context.Context, req *csi.NodeGetVolumeStatsRequest) (*csi.NodeGetVolumeStatsResponse, error) {
	glog.V(5).Infof("NodeGetVolumeStats called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeGetVolumeStats is not implemented")
}

// NodePublishVolume implements csi.NodeServer.
func (n *nodeServer) NodePublishVolume(ctx context.Context, req *csi.NodePublishVolumeRequest) (*csi.NodePublishVolumeResponse, error) {
	glog.V(5).Infof("NodePublishVolumeRequest called with request: %s", req.String())
	targetPath := req.GetTargetPath()
	if len(targetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodePublishVolume operation requires targetPath but is not provided")
	}
	// The lock is to avoid race condition
	if lock := n.locks.TryAcquire(targetPath); !lock {
		return nil, status.Errorf(codes.Aborted, "NodePublishVolume operation on targetPath %s already exists", targetPath)
	}
	defer n.locks.Release(targetPath)
	return &csi.NodePublishVolumeResponse{}, nil
}

// NodeStageVolume implements csi.NodeServer.
func (n *nodeServer) NodeStageVolume(ctx context.Context, req *csi.NodeStageVolumeRequest) (*csi.NodeStageVolumeResponse, error) {
	glog.V(5).Infof("NodeStageVolume called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeStageVolume is not implemented")
}

// NodeUnpublishVolume implements csi.NodeServer.
func (n *nodeServer) NodeUnpublishVolume(ctx context.Context, req *csi.NodeUnpublishVolumeRequest) (*csi.NodeUnpublishVolumeResponse, error) {
	glog.V(5).Infof("NodeUnpublishVolume called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeUnpublishVolume is not implemented")
}

// NodeUnstageVolume implements csi.NodeServer.
func (n *nodeServer) NodeUnstageVolume(ctx context.Context, req *csi.NodeUnstageVolumeRequest) (*csi.NodeUnstageVolumeResponse, error) {
	glog.V(5).Infof("NodeUnstageVolume called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeUnstageVolume is not implemented")
}
