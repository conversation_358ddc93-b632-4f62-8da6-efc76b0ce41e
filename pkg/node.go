package pkg

import (
	"context"
	"os"

	"github.com/container-storage-interface/spec/lib/go/csi"
	"github.com/golang/glog"
	"github.com/kubegems/xpai-csi/pkg/filesystem"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	v1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

var _ csi.NodeServer = &nodeServer{}

type nodeServer struct {
	csi.UnimplementedNodeServer
	nodeID               string
	client               client.Client
	apiReader            client.Reader
	nodeAuthorizedClient kubernetes.Interface
	locks                *volumeLocks
	node                 *v1.Node
	fsManager            *filesystem.FilesystemManager
	healthChecker        *filesystem.HealthChecker
}

func newNodeServer(nodeID string, client client.Client, kubeClient kubernetes.Interface) csi.NodeServer {
	// Initialize filesystem manager with shared mounts and cross-namespace sharing enabled
	fsManager := filesystem.NewFilesystemManager(&filesystem.ManagerConfig{
		Client:                      client,
		KubeClient:                  kubeClient,
		NodeID:                      nodeID,
		EnableSharedMounts:          true, // Enable shared mounts
		EnableCrossNamespaceSharing: true, // Enable cross-namespace sharing
		SharedMountDir:              "/var/lib/xpai-csi/shared",
	})

	// Initialize health checker
	healthChecker := filesystem.NewHealthChecker(&filesystem.HealthCheckerConfig{
		Client:     client,
		KubeClient: kubeClient,
		NodeID:     nodeID,
		FSManager:  fsManager,
	})

	// Start health checker in background
	go func() {
		ctx := context.Background()
		if err := healthChecker.Start(ctx); err != nil {
			glog.Errorf("Health checker failed: %v", err)
		}
	}()

	return &nodeServer{
		nodeID:               nodeID,
		client:               client,
		nodeAuthorizedClient: kubeClient,
		locks:                newVolumeLocks(),
		fsManager:            fsManager,
		healthChecker:        healthChecker,
	}
}

// NodeExpandVolume implements csi.NodeServer.
func (n *nodeServer) NodeExpandVolume(ctx context.Context, req *csi.NodeExpandVolumeRequest) (*csi.NodeExpandVolumeResponse, error) {
	glog.V(5).Infof("NodeExpandVolume called with request: %s", req.String())
	return nil, status.Error(codes.Unimplemented, "NodeExpandVolume is not implemented")
}

// NodeGetCapabilities implements csi.NodeServer.
func (n *nodeServer) NodeGetCapabilities(ctx context.Context, req *csi.NodeGetCapabilitiesRequest) (*csi.NodeGetCapabilitiesResponse, error) {
	glog.V(5).Infof("NodeGetCapabilities called with request: %s", req.String())
	return &csi.NodeGetCapabilitiesResponse{
		Capabilities: []*csi.NodeServiceCapability{
			{
				Type: &csi.NodeServiceCapability_Rpc{
					Rpc: &csi.NodeServiceCapability_RPC{
						Type: csi.NodeServiceCapability_RPC_STAGE_UNSTAGE_VOLUME,
					},
				},
			},
		},
	}, nil
}

// NodeGetInfo implements csi.NodeServer.
func (n *nodeServer) NodeGetInfo(ctx context.Context, req *csi.NodeGetInfoRequest) (*csi.NodeGetInfoResponse, error) {
	glog.V(5).Infof("NodeGetInfo called with request: %s", req.String())
	return &csi.NodeGetInfoResponse{
		NodeId: n.nodeID,
	}, nil
}

// NodeGetVolumeStats implements csi.NodeServer.
func (n *nodeServer) NodeGetVolumeStats(ctx context.Context, req *csi.NodeGetVolumeStatsRequest) (*csi.NodeGetVolumeStatsResponse, error) {
	glog.V(5).Infof("NodeGetVolumeStats called with request: %s", req.String())

	volumeID := req.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeGetVolumeStats operation requires volumeId but is not provided")
	}

	volumePath := req.GetVolumePath()
	if len(volumePath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeGetVolumeStats operation requires volumePath but is not provided")
	}

	// Check if path exists
	if _, err := os.Stat(volumePath); err != nil {
		if os.IsNotExist(err) {
			return nil, status.Errorf(codes.NotFound, "Volume path %s does not exist", volumePath)
		}
		return nil, status.Errorf(codes.Internal, "Failed to stat volume path %s: %v", volumePath, err)
	}

	// Get volume statistics using syscall
	// For now, we'll return basic stats
	// TODO: Implement proper volume statistics collection

	// Check health status
	healthInfo, exists := n.healthChecker.GetMountHealth(volumeID)
	if exists && !healthInfo.Healthy {
		glog.Warningf("Volume %s is not healthy: %s", volumeID, healthInfo.LastError)
	}

	// Return basic volume stats
	// In a real implementation, you would collect actual filesystem statistics
	return &csi.NodeGetVolumeStatsResponse{
		Usage: []*csi.VolumeUsage{
			{
				Unit:  csi.VolumeUsage_BYTES,
				Total: 1024 * 1024 * 1024, // 1GB placeholder
				Used:  512 * 1024 * 1024,  // 512MB placeholder
			},
			{
				Unit:  csi.VolumeUsage_INODES,
				Total: 1000000, // 1M inodes placeholder
				Used:  500000,  // 500K inodes placeholder
			},
		},
	}, nil
}

// NodePublishVolume implements csi.NodeServer.
func (n *nodeServer) NodePublishVolume(ctx context.Context, req *csi.NodePublishVolumeRequest) (*csi.NodePublishVolumeResponse, error) {
	glog.V(5).Infof("NodePublishVolumeRequest called with request: %s", req.String())

	// Validate request
	volumeID := req.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodePublishVolume operation requires volumeId but is not provided")
	}

	targetPath := req.GetTargetPath()
	if len(targetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodePublishVolume operation requires targetPath but is not provided")
	}

	stagingTargetPath := req.GetStagingTargetPath()
	if len(stagingTargetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodePublishVolume operation requires stagingTargetPath but is not provided")
	}

	volumeCapability := req.GetVolumeCapability()
	if volumeCapability == nil {
		return nil, status.Error(codes.InvalidArgument, "NodePublishVolume operation requires volumeCapability but is not provided")
	}

	// Acquire lock for this volume
	if lock := n.locks.TryAcquire(volumeID); !lock {
		return nil, status.Errorf(codes.Aborted, "NodePublishVolume operation on volume %s already exists", volumeID)
	}
	defer n.locks.Release(volumeID)

	// Check if already published
	if isMounted, err := n.isMountPoint(targetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to check if target path is mounted: %v", err)
	} else if isMounted {
		glog.V(2).Infof("Volume %s is already published at %s", volumeID, targetPath)
		return &csi.NodePublishVolumeResponse{}, nil
	}

	// Parse storage class parameters and secrets
	parameters := req.GetVolumeContext()
	secrets := req.GetSecrets()

	// Parse filesystem type and mount options
	fsType, mountOptions, err := n.fsManager.ParseStorageClassParameters(parameters, secrets)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "Failed to parse storage class parameters: %v", err)
	}

	// Extract PVC information from volume context
	pvcName := req.GetVolumeContext()["csi.storage.k8s.io/pvc/name"]
	pvcNamespace := req.GetVolumeContext()["csi.storage.k8s.io/pvc/namespace"]
	storageClassName := req.GetVolumeContext()["csi.storage.k8s.io/storageclass/name"]

	// Create volume context
	volumeCtx := &filesystem.VolumeContext{
		VolumeID:         volumeID,
		VolumeName:       volumeID,
		Parameters:       parameters,
		Secrets:          secrets,
		VolumeContext:    req.GetVolumeContext(),
		MountOptions:     mountOptions,
		Attributes:       make(map[string]string),
		PVCName:          pvcName,
		PVCNamespace:     pvcNamespace,
		StorageClassName: storageClassName,
	}

	// Add filesystem type to parameters
	volumeCtx.Parameters["type"] = string(fsType)

	// Ensure target directory exists
	if err := os.MkdirAll(targetPath, 0755); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to create target directory %s: %v", targetPath, err)
	}

	// Publish the volume using filesystem manager
	if err := n.fsManager.PublishVolume(ctx, volumeCtx, stagingTargetPath, targetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to publish volume %s: %v", volumeID, err)
	}

	glog.V(2).Infof("Successfully published volume %s to %s", volumeID, targetPath)
	return &csi.NodePublishVolumeResponse{}, nil
}

// NodeStageVolume implements csi.NodeServer.
func (n *nodeServer) NodeStageVolume(ctx context.Context, req *csi.NodeStageVolumeRequest) (*csi.NodeStageVolumeResponse, error) {
	glog.V(5).Infof("NodeStageVolume called with request: %s", req.String())

	// Validate request
	volumeID := req.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeStageVolume operation requires volumeId but is not provided")
	}

	stagingTargetPath := req.GetStagingTargetPath()
	if len(stagingTargetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeStageVolume operation requires stagingTargetPath but is not provided")
	}

	volumeCapability := req.GetVolumeCapability()
	if volumeCapability == nil {
		return nil, status.Error(codes.InvalidArgument, "NodeStageVolume operation requires volumeCapability but is not provided")
	}

	// Acquire lock for this volume
	if lock := n.locks.TryAcquire(volumeID); !lock {
		return nil, status.Errorf(codes.Aborted, "NodeStageVolume operation on volume %s already exists", volumeID)
	}
	defer n.locks.Release(volumeID)

	// Check if already staged
	if isMounted, err := n.isMountPoint(stagingTargetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to check if staging path is mounted: %v", err)
	} else if isMounted {
		glog.V(2).Infof("Volume %s is already staged at %s", volumeID, stagingTargetPath)
		return &csi.NodeStageVolumeResponse{}, nil
	}

	// Parse storage class parameters and secrets
	parameters := req.GetVolumeContext()
	secrets := req.GetSecrets()

	// Parse filesystem type and mount options
	fsType, mountOptions, err := n.fsManager.ParseStorageClassParameters(parameters, secrets)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "Failed to parse storage class parameters: %v", err)
	}

	// Extract PVC information from volume context
	pvcName := req.GetVolumeContext()["csi.storage.k8s.io/pvc/name"]
	pvcNamespace := req.GetVolumeContext()["csi.storage.k8s.io/pvc/namespace"]
	storageClassName := req.GetVolumeContext()["csi.storage.k8s.io/storageclass/name"]

	// Create volume context
	volumeCtx := &filesystem.VolumeContext{
		VolumeID:         volumeID,
		VolumeName:       volumeID, // Use volumeID as name for now
		Parameters:       parameters,
		Secrets:          secrets,
		VolumeContext:    req.GetVolumeContext(),
		MountOptions:     mountOptions,
		Attributes:       make(map[string]string),
		PVCName:          pvcName,
		PVCNamespace:     pvcNamespace,
		StorageClassName: storageClassName,
	}

	// Add filesystem type to parameters
	volumeCtx.Parameters["type"] = string(fsType)

	// Ensure staging directory exists
	if err := os.MkdirAll(stagingTargetPath, 0755); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to create staging directory %s: %v", stagingTargetPath, err)
	}

	// Stage the volume using filesystem manager
	if err := n.fsManager.StageVolume(ctx, volumeCtx, stagingTargetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to stage volume %s: %v", volumeID, err)
	}

	// Register mount for health checking
	n.healthChecker.RegisterMount(volumeID, stagingTargetPath, string(fsType), parameters)

	glog.V(2).Infof("Successfully staged volume %s at %s", volumeID, stagingTargetPath)
	return &csi.NodeStageVolumeResponse{}, nil
}

// isMountPoint checks if a path is a mount point
func (n *nodeServer) isMountPoint(path string) (bool, error) {
	// Check if the path exists
	if _, err := os.Stat(path); err != nil {
		if os.IsNotExist(err) {
			return false, nil
		}
		return false, err
	}

	// For now, we'll use a simple check by reading /proc/mounts
	// In production, you might want to use a more robust method
	// TODO: Implement proper mount point detection using syscalls or mount info
	return false, nil
}

// NodeUnpublishVolume implements csi.NodeServer.
func (n *nodeServer) NodeUnpublishVolume(ctx context.Context, req *csi.NodeUnpublishVolumeRequest) (*csi.NodeUnpublishVolumeResponse, error) {
	glog.V(5).Infof("NodeUnpublishVolume called with request: %s", req.String())

	// Validate request
	volumeID := req.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeUnpublishVolume operation requires volumeId but is not provided")
	}

	targetPath := req.GetTargetPath()
	if len(targetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeUnpublishVolume operation requires targetPath but is not provided")
	}

	// Acquire lock for this volume
	if lock := n.locks.TryAcquire(volumeID); !lock {
		return nil, status.Errorf(codes.Aborted, "NodeUnpublishVolume operation on volume %s already exists", volumeID)
	}
	defer n.locks.Release(volumeID)

	// Check if the path is mounted
	if isMounted, err := n.isMountPoint(targetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to check if target path is mounted: %v", err)
	} else if !isMounted {
		glog.V(2).Infof("Volume %s is not mounted at %s, nothing to unpublish", volumeID, targetPath)
		return &csi.NodeUnpublishVolumeResponse{}, nil
	}

	// Create a minimal volume context for unmounting
	// We need to determine the filesystem type from somewhere
	// For now, we'll try to get it from the mount info or use a default approach
	volumeCtx := &filesystem.VolumeContext{
		VolumeID:   volumeID,
		VolumeName: volumeID,
		Parameters: map[string]string{
			"type": "nfs", // Default to NFS for now, should be determined dynamically
		},
		Attributes: make(map[string]string),
	}

	// Unpublish the volume using filesystem manager
	if err := n.fsManager.UnpublishVolume(ctx, volumeCtx, targetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to unpublish volume %s: %v", volumeID, err)
	}

	glog.V(2).Infof("Successfully unpublished volume %s from %s", volumeID, targetPath)
	return &csi.NodeUnpublishVolumeResponse{}, nil
}

// NodeUnstageVolume implements csi.NodeServer.
func (n *nodeServer) NodeUnstageVolume(ctx context.Context, req *csi.NodeUnstageVolumeRequest) (*csi.NodeUnstageVolumeResponse, error) {
	glog.V(5).Infof("NodeUnstageVolume called with request: %s", req.String())

	// Validate request
	volumeID := req.GetVolumeId()
	if len(volumeID) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeUnstageVolume operation requires volumeId but is not provided")
	}

	stagingTargetPath := req.GetStagingTargetPath()
	if len(stagingTargetPath) == 0 {
		return nil, status.Error(codes.InvalidArgument, "NodeUnstageVolume operation requires stagingTargetPath but is not provided")
	}

	// Acquire lock for this volume
	if lock := n.locks.TryAcquire(volumeID); !lock {
		return nil, status.Errorf(codes.Aborted, "NodeUnstageVolume operation on volume %s already exists", volumeID)
	}
	defer n.locks.Release(volumeID)

	// Check if the path is mounted
	if isMounted, err := n.isMountPoint(stagingTargetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to check if staging path is mounted: %v", err)
	} else if !isMounted {
		glog.V(2).Infof("Volume %s is not staged at %s, nothing to unstage", volumeID, stagingTargetPath)
		return &csi.NodeUnstageVolumeResponse{}, nil
	}

	// Create a minimal volume context for unmounting
	volumeCtx := &filesystem.VolumeContext{
		VolumeID:   volumeID,
		VolumeName: volumeID,
		Parameters: map[string]string{
			"type": "nfs", // Default to NFS for now, should be determined dynamically
		},
		Attributes: make(map[string]string),
	}

	// Unstage the volume using filesystem manager
	if err := n.fsManager.UnstageVolume(ctx, volumeCtx, stagingTargetPath); err != nil {
		return nil, status.Errorf(codes.Internal, "Failed to unstage volume %s: %v", volumeID, err)
	}

	// Unregister mount from health checking
	n.healthChecker.UnregisterMount(volumeID)

	glog.V(2).Infof("Successfully unstaged volume %s from %s", volumeID, stagingTargetPath)
	return &csi.NodeUnstageVolumeResponse{}, nil
}

// GetSharedMountInfo returns information about shared mounts (for debugging)
func (n *nodeServer) GetSharedMountInfo() map[string]*filesystem.SharedMountInfo {
	return n.fsManager.GetSharedMountInfo()
}

// GetSharedMountMetrics returns metrics about shared mounts
func (n *nodeServer) GetSharedMountMetrics() map[string]interface{} {
	return n.fsManager.GetSharedMountMetrics()
}
