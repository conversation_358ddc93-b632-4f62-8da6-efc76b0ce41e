#!/usr/bin/env bash
set -xe

# Function to check if a directory exists
check_kubelet_rootdir_subfolder() {
  local dir="$1"
  
  if [ ! -d "$dir" ]; then
    echo "Error: subfolder $dir does not exist, please check whether KUBELET_ROOTDIR $KUBELET_ROOTDIR is configured correctly." 
    exit 1
  fi
}

check_kubelet_rootdir_subfolder "$KUBELET_ROOTDIR/pods"
check_kubelet_rootdir_subfolder "$KUBELET_ROOTDIR/plugins"

rm -f "$KUBELET_ROOTDIR/csi-plugins/fuse.csi.xiaoshiai.cn/csi.sock"
mkdir -p "$KUBELET_ROOTDIR/csi-plugins/fuse.csi.xiaoshiai.cn"

xpai-csi start $@
