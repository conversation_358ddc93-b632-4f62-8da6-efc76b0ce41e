#!/bin/bash

# XPAI CSI 驱动调试脚本
# 自动化常见的调试任务和信息收集

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查 kubectl 是否可用
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
XPAI CSI 驱动调试脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -s, --status            检查 CSI 驱动状态
    -l, --logs              收集 CSI 驱动日志
    -p, --pods              检查 Pod 和 PVC 状态
    -m, --mounts            检查挂载点状态
    -c, --cross-ns          检查跨 namespace 共享状态
    -n, --namespace NAME    指定 namespace (默认: 所有)
    -v, --volume ID         检查特定 volume
    -a, --all               执行所有检查
    --collect-logs          收集所有日志到文件
    --debug-pod NAME        调试特定 Pod 的挂载问题

示例:
    $0 --status                    # 检查 CSI 驱动状态
    $0 --logs                      # 查看实时日志
    $0 --cross-ns                  # 检查跨 namespace 共享
    $0 --debug-pod my-pod          # 调试特定 Pod
    $0 --all                       # 执行所有检查
EOF
}

# 检查 CSI 驱动状态
check_csi_status() {
    log_info "检查 CSI 驱动状态..."
    
    echo "=== CSI Driver 注册状态 ==="
    kubectl get csidriver fuse.csi.xiaoshiai.cn -o wide 2>/dev/null || log_warn "CSI Driver 未注册"
    
    echo -e "\n=== CSI Node 状态 ==="
    kubectl get csinode -o wide
    
    echo -e "\n=== CSI Pod 状态 ==="
    kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai -o wide
    kubectl get pods -n kube-system -l app=csi-controller-xpai -o wide
    
    echo -e "\n=== StorageClass 状态 ==="
    kubectl get storageclass | grep -E "(NAME|xpai)" || log_warn "未找到 XPAI StorageClass"
    
    echo -e "\n=== Mount Pod 状态 ==="
    kubectl get pods -n kube-system -l xpai-csi/mount-pod=true -o wide || log_info "未找到 mount-pod"
}

# 收集 CSI 驱动日志
collect_logs() {
    local follow=${1:-false}
    
    log_info "收集 CSI 驱动日志..."
    
    if [ "$follow" = "true" ]; then
        log_info "实时查看日志 (Ctrl+C 退出)..."
        kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins -f --tail=100
    else
        echo "=== Node Plugin 日志 (最近 50 行) ==="
        kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins --tail=50
        
        echo -e "\n=== Controller Plugin 日志 (最近 50 行) ==="
        kubectl logs -n kube-system -l app=csi-controller-xpai -c plugins --tail=50 2>/dev/null || log_warn "Controller Plugin 未运行"
        
        echo -e "\n=== Mount Pod 日志 ==="
        for pod in $(kubectl get pods -n kube-system -l xpai-csi/mount-pod=true -o name 2>/dev/null); do
            echo "--- $pod ---"
            kubectl logs -n kube-system $pod --tail=20
        done
    fi
}

# 检查 Pod 和 PVC 状态
check_pods_pvcs() {
    local namespace=${1:-""}
    
    log_info "检查 Pod 和 PVC 状态..."
    
    if [ -n "$namespace" ]; then
        ns_flag="-n $namespace"
        log_info "检查 namespace: $namespace"
    else
        ns_flag="-A"
        log_info "检查所有 namespace"
    fi
    
    echo "=== PVC 状态 ==="
    kubectl get pvc $ns_flag -o wide
    
    echo -e "\n=== PV 状态 ==="
    kubectl get pv -o wide
    
    echo -e "\n=== 使用 CSI 卷的 Pod ==="
    kubectl get pods $ns_flag -o jsonpath='{range .items[*]}{.metadata.namespace}{"\t"}{.metadata.name}{"\t"}{.spec.volumes[*].persistentVolumeClaim.claimName}{"\n"}{end}' | grep -v "^$" | column -t
    
    echo -e "\n=== Pending 状态的 PVC ==="
    kubectl get pvc $ns_flag | grep Pending || log_info "没有 Pending 状态的 PVC"
    
    echo -e "\n=== 有问题的 Pod ==="
    kubectl get pods $ns_flag | grep -E "(Error|CrashLoopBackOff|ImagePullBackOff|Pending)" || log_info "没有有问题的 Pod"
}

# 检查挂载点状态
check_mounts() {
    log_info "检查挂载点状态..."
    
    echo "=== CSI 挂载点 ==="
    for node_pod in $(kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai -o name); do
        node_name=$(kubectl get $node_pod -n kube-system -o jsonpath='{.spec.nodeName}')
        echo "--- Node: $node_name ---"
        kubectl exec -n kube-system $node_pod -c plugins -- mount | grep -E "(xpai|csi)" || log_info "该节点上没有 CSI 挂载点"
    done
    
    echo -e "\n=== 共享挂载目录 ==="
    for node_pod in $(kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai -o name); do
        node_name=$(kubectl get $node_pod -n kube-system -o jsonpath='{.spec.nodeName}')
        echo "--- Node: $node_name ---"
        kubectl exec -n kube-system $node_pod -c plugins -- ls -la /var/lib/xpai-csi/shared/ 2>/dev/null || log_info "共享挂载目录不存在"
    done
}

# 检查跨 namespace 共享状态
check_cross_namespace() {
    log_info "检查跨 namespace 共享状态..."
    
    echo "=== 启用跨 namespace 共享的 StorageClass ==="
    kubectl get storageclass -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.parameters.enableCrossNamespaceSharing}{"\n"}{end}' | grep true || log_info "没有启用跨 namespace 共享的 StorageClass"
    
    echo -e "\n=== 跨 namespace 的 Volume ID ==="
    kubectl get pv -o jsonpath='{range .items[*]}{.spec.csi.volumeHandle}{"\n"}{end}' | grep "cross-ns" | sort | uniq -c || log_info "没有跨 namespace 共享的卷"
    
    echo -e "\n=== 相同名称的 PVC (可能共享) ==="
    kubectl get pvc -A -o jsonpath='{range .items[*]}{.metadata.name}{"\t"}{.metadata.namespace}{"\t"}{.spec.storageClassName}{"\n"}{end}' | sort | uniq -c | awk '$1 > 1 {print}'
    
    echo -e "\n=== Mount Pod 数量 vs PVC 数量 ==="
    mount_pods=$(kubectl get pods -n kube-system -l xpai-csi/mount-pod=true --no-headers 2>/dev/null | wc -l)
    total_pvcs=$(kubectl get pvc -A --no-headers | wc -l)
    echo "Mount Pods: $mount_pods"
    echo "Total PVCs: $total_pvcs"
    if [ $mount_pods -lt $total_pvcs ]; then
        log_info "共享挂载正在工作 (Mount Pod 数量 < PVC 数量)"
    fi
}

# 调试特定 Pod
debug_pod() {
    local pod_name=$1
    local namespace=${2:-"default"}
    
    log_info "调试 Pod: $pod_name (namespace: $namespace)"
    
    echo "=== Pod 状态 ==="
    kubectl describe pod $pod_name -n $namespace
    
    echo -e "\n=== Pod 使用的 PVC ==="
    kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.volumes[*].persistentVolumeClaim.claimName}' | tr ' ' '\n'
    
    echo -e "\n=== 相关 PVC 详情 ==="
    for pvc in $(kubectl get pod $pod_name -n $namespace -o jsonpath='{.spec.volumes[*].persistentVolumeClaim.claimName}'); do
        echo "--- PVC: $pvc ---"
        kubectl describe pvc $pvc -n $namespace
    done
    
    echo -e "\n=== Pod 事件 ==="
    kubectl get events -n $namespace --field-selector involvedObject.name=$pod_name --sort-by='.lastTimestamp'
}

# 收集所有日志到文件
collect_all_logs() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local log_dir="csi-debug-logs-$timestamp"
    
    log_info "收集所有日志到目录: $log_dir"
    mkdir -p $log_dir
    
    # CSI 驱动日志
    kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins > $log_dir/csi-node.log 2>&1
    kubectl logs -n kube-system -l app=csi-controller-xpai -c plugins > $log_dir/csi-controller.log 2>&1
    
    # Mount Pod 日志
    kubectl get pods -n kube-system -l xpai-csi/mount-pod=true -o name | while read pod; do
        pod_name=$(basename $pod)
        kubectl logs -n kube-system $pod > $log_dir/mount-pod-$pod_name.log 2>&1
    done
    
    # 状态信息
    kubectl get csidriver,csinode,storageclass,pv,pvc -A -o wide > $log_dir/csi-resources.txt 2>&1
    kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai -o yaml > $log_dir/csi-pods.yaml 2>&1
    kubectl describe pods -n kube-system -l app=csi-nodeplugin-xpai > $log_dir/csi-pods-describe.txt 2>&1
    
    log_info "日志收集完成: $log_dir"
    log_info "可以将此目录打包发送给开发团队进行分析"
}

# 检查特定 volume
check_volume() {
    local volume_id=$1
    
    log_info "检查 Volume: $volume_id"
    
    echo "=== Volume 详情 ==="
    kubectl get pv -o jsonpath="{range .items[*]}{.metadata.name}{'\t'}{.spec.csi.volumeHandle}{'\n'}{end}" | grep $volume_id
    
    echo -e "\n=== 使用此 Volume 的 PVC ==="
    for pv in $(kubectl get pv -o jsonpath="{range .items[*]}{.metadata.name}{'\t'}{.spec.csi.volumeHandle}{'\n'}{end}" | grep $volume_id | cut -f1); do
        kubectl get pvc -A -o jsonpath="{range .items[*]}{.metadata.namespace}{'\t'}{.metadata.name}{'\t'}{.spec.volumeName}{'\n'}{end}" | grep $pv
    done
    
    echo -e "\n=== 相关日志 ==="
    kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep $volume_id | tail -20
}

# 主函数
main() {
    check_kubectl
    
    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -s|--status)
            check_csi_status
            ;;
        -l|--logs)
            collect_logs true
            ;;
        -p|--pods)
            check_pods_pvcs "${2:-}"
            ;;
        -m|--mounts)
            check_mounts
            ;;
        -c|--cross-ns)
            check_cross_namespace
            ;;
        -v|--volume)
            if [ -z "${2:-}" ]; then
                log_error "请指定 volume ID"
                exit 1
            fi
            check_volume "$2"
            ;;
        -a|--all)
            check_csi_status
            echo -e "\n" && check_pods_pvcs
            echo -e "\n" && check_mounts
            echo -e "\n" && check_cross_namespace
            ;;
        --collect-logs)
            collect_all_logs
            ;;
        --debug-pod)
            if [ -z "${2:-}" ]; then
                log_error "请指定 Pod 名称"
                exit 1
            fi
            debug_pod "$2" "${3:-default}"
            ;;
        "")
            log_info "XPAI CSI 驱动调试脚本"
            log_info "使用 --help 查看帮助信息"
            check_csi_status
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
