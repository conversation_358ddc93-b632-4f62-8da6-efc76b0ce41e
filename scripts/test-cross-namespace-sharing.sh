#!/bin/bash

# Test script for cross-namespace sharing functionality
# This script verifies that:
# 1. Same PVC name + Same StorageClass = Sharing
# 2. Same PVC name + Different StorageClass = No sharing (isolation)
# 3. Different PVC name + Same StorageClass = No sharing

set -e

echo "=== XPAI CSI Cross-Namespace Sharing Test ==="
echo "This script tests the cross-namespace sharing functionality"
echo "and verifies proper StorageClass isolation."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    log_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if CSI driver is running
log_info "Checking if XPAI CSI driver is running..."
if ! kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai | grep -q Running; then
    log_error "XPAI CSI driver is not running"
    exit 1
fi
log_info "CSI driver is running"

# Test 1: Cross-namespace sharing with same StorageClass
log_info "Test 1: Cross-namespace sharing (same StorageClass)"
echo "Deploying cross-namespace sharing demo..."
kubectl apply -f examples/cross-namespace-sharing-demo.yaml

# Wait for PVCs to be bound
log_info "Waiting for PVCs to be bound..."
sleep 30

# Check PVCs
log_info "Checking PVC status..."
kubectl get pvc app-data -n app-namespace-a -o wide
kubectl get pvc app-data -n app-namespace-b -o wide
kubectl get pvc app-data -n app-namespace-c -o wide

# Check mount pods (should be only 1 for cross-namespace sharing)
log_info "Checking mount pods..."
MOUNT_PODS=$(kubectl get pods -n kube-system -l xpai-csi/mount-pod=true --no-headers | wc -l)
echo "Number of mount pods: $MOUNT_PODS"

if [ "$MOUNT_PODS" -eq 1 ]; then
    log_info "✓ Cross-namespace sharing working: Only 1 mount pod for 3 PVCs"
else
    log_warn "⚠ Expected 1 mount pod, found $MOUNT_PODS"
fi

# Wait for pods to start
log_info "Waiting for application pods to start..."
sleep 60

# Check if pods can see shared files
log_info "Testing file sharing between namespaces..."
kubectl exec -n app-namespace-a app-pod-a -- touch /shared-data/test-from-a.txt 2>/dev/null || true
sleep 5

# Check if other namespaces can see the file
if kubectl exec -n app-namespace-b app-pod-b -- ls /shared-data/test-from-a.txt &>/dev/null; then
    log_info "✓ File sharing working: Namespace B can see file from Namespace A"
else
    log_warn "⚠ File sharing not working: Namespace B cannot see file from Namespace A"
fi

echo ""

# Test 2: StorageClass isolation
log_info "Test 2: StorageClass isolation (different StorageClasses)"
echo "Deploying StorageClass isolation demo..."
kubectl apply -f examples/storageclass-isolation-demo.yaml

# Wait for PVCs to be bound
log_info "Waiting for isolation demo PVCs to be bound..."
sleep 30

# Check mount pods (should be more now due to different StorageClasses)
log_info "Checking mount pods after isolation demo..."
MOUNT_PODS_AFTER=$(kubectl get pods -n kube-system -l xpai-csi/mount-pod=true --no-headers | wc -l)
echo "Number of mount pods after isolation demo: $MOUNT_PODS_AFTER"

if [ "$MOUNT_PODS_AFTER" -gt "$MOUNT_PODS" ]; then
    log_info "✓ StorageClass isolation working: Additional mount pods created for different StorageClasses"
else
    log_warn "⚠ StorageClass isolation may not be working properly"
fi

# Wait for isolation demo pods to start
log_info "Waiting for isolation demo pods to start..."
sleep 60

# Test isolation between teams
log_info "Testing isolation between different StorageClasses..."
kubectl exec -n team-a team-a-app -- touch /data/team-a-secret.txt 2>/dev/null || true
sleep 5

# Check if team-b can see team-a's file (should NOT be able to)
if kubectl exec -n team-b team-b-app -- ls /data/team-a-secret.txt &>/dev/null; then
    log_error "✗ StorageClass isolation FAILED: Team B can see Team A's files"
else
    log_info "✓ StorageClass isolation working: Team B cannot see Team A's files"
fi

echo ""

# Summary
log_info "=== Test Summary ==="
echo "1. Cross-namespace sharing test: Check logs above for results"
echo "2. StorageClass isolation test: Check logs above for results"
echo ""
echo "Manual verification commands:"
echo "# Check mount pods:"
echo "kubectl get pods -n kube-system -l xpai-csi/mount-pod=true"
echo ""
echo "# Check cross-namespace sharing:"
echo "kubectl logs -n app-namespace-a app-pod-a | tail -20"
echo "kubectl logs -n app-namespace-b app-pod-b | tail -20"
echo ""
echo "# Check StorageClass isolation:"
echo "kubectl logs -n team-a team-a-app | tail -20"
echo "kubectl logs -n team-b team-b-app | tail -20"
echo ""
echo "# Check CSI driver logs:"
echo "kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i cross"
echo ""

# Cleanup option
read -p "Do you want to clean up test resources? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    log_info "Cleaning up test resources..."
    kubectl delete -f examples/cross-namespace-sharing-demo.yaml --ignore-not-found=true
    kubectl delete -f examples/storageclass-isolation-demo.yaml --ignore-not-found=true
    log_info "Cleanup completed"
else
    log_info "Test resources left for manual inspection"
    echo "To clean up later, run:"
    echo "kubectl delete -f examples/cross-namespace-sharing-demo.yaml"
    echo "kubectl delete -f examples/storageclass-isolation-demo.yaml"
fi

echo ""
log_info "Cross-namespace sharing test completed!"
