# Shared Mounts Feature

## Overview

The XPAI CSI driver supports **shared mounts** to optimize resource usage when multiple PVCs use the same StorageClass with identical parameters on the same node. Instead of creating separate mount-pods for each PVC, the driver creates a single shared mount and uses bind mounts to provide access to individual PVCs.

## How It Works

### Traditional Approach (Without Shared Mounts)
```
PVC-1 (NFS: server1:/data) → Mount-Pod-1 → /var/lib/kubelet/pods/.../pvc-1
PVC-2 (NFS: server1:/data) → Mount-Pod-2 → /var/lib/kubelet/pods/.../pvc-2  
PVC-3 (NFS: server1:/data) → Mount-Pod-3 → /var/lib/kubelet/pods/.../pvc-3
```
**Result**: 3 mount-pods, 3 NFS connections

### Shared Mounts Approach
```
PVC-1 (NFS: server1:/data) ┐
PVC-2 (NFS: server1:/data) ├→ Shared-Mount-Pod → /var/lib/xpai-csi/shared/abc123
PVC-3 (NFS: server1:/data) ┘                      ↓ (bind mounts)
                                                   ├→ /var/lib/kubelet/pods/.../pvc-1
                                                   ├→ /var/lib/kubelet/pods/.../pvc-2
                                                   └→ /var/lib/kubelet/pods/.../pvc-3
```
**Result**: 1 mount-pod, 1 NFS connection, 3 bind mounts

## Benefits

1. **Resource Efficiency**: Fewer mount-pods reduce CPU and memory usage
2. **Network Efficiency**: Single connection to the filesystem server
3. **Faster Mount Times**: Subsequent PVCs mount instantly via bind mounts
4. **Reduced Server Load**: Less load on NFS/CephFS/other filesystem servers

## Configuration

### Enable Shared Mounts

In your Helm values:

```yaml
csi:
  sharedMounts:
    enabled: true
    sharedDir: /var/lib/xpai-csi/shared
    cleanupInterval: 1h
```

### Environment Variables

The following environment variables control shared mount behavior:

- `SHARED_MOUNTS_ENABLED`: Enable/disable shared mounts (default: false)
- `SHARED_MOUNTS_DIR`: Directory for shared mounts (default: /var/lib/xpai-csi/shared)
- `SHARED_MOUNTS_CLEANUP_INTERVAL`: Cleanup interval for unused mounts (default: 1h)

## Supported Filesystems

Shared mounts are supported for the following filesystem types:

- ✅ **NFS**: Full support
- ✅ **CephFS**: Full support  
- ✅ **GlusterFS**: Full support
- ✅ **JuiceFS**: Full support
- ❌ **Generic FUSE**: Disabled by default (may not be safe for all FUSE filesystems)

## Mount Key Generation

The system generates a unique mount key based on:

- Filesystem type
- Server address and path
- Mount options and flags
- Filesystem-specific parameters (NFS version, Ceph monitors, etc.)

PVCs with identical mount keys will share the same mount.

## Example

### StorageClass
```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-nfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "*************"
  path: "/exports/data"
  nfsVersion: "4"
```

### Multiple PVCs
```yaml
# All these PVCs will share the same mount
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data-1
spec:
  storageClassName: shared-nfs
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data-2
spec:
  storageClassName: shared-nfs
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi
```

## Monitoring

### Check Shared Mounts

```bash
# List shared mount pods
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true

# Check shared mount directory on nodes
ls -la /var/lib/xpai-csi/shared/

# View CSI driver logs for shared mount information
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i shared
```

### Metrics

The CSI driver exposes metrics about shared mounts:

- `total_shared_mounts`: Number of active shared mounts
- `total_volumes`: Total number of volumes using shared mounts
- `shared_mounts_enabled`: Whether shared mounts are enabled

## Lifecycle Management

### Mount Creation
1. First PVC triggers creation of shared mount
2. Mount-pod creates the filesystem mount
3. Shared mount directory is created
4. PVC gets bind mount from shared directory

### Additional PVCs
1. System detects matching mount key
2. Reference count is incremented
3. Bind mount is created immediately
4. No additional mount-pod is needed

### Mount Cleanup
1. When PVC is deleted, reference count decreases
2. When reference count reaches zero, shared mount is scheduled for cleanup
3. Cleanup happens after configurable delay (default: 1h)
4. Mount-pod unmounts and shared directory is removed

## Troubleshooting

### Common Issues

1. **Bind Mount Failures**
   ```bash
   # Check if shared mount exists
   ls -la /var/lib/xpai-csi/shared/
   
   # Check mount-pod logs
   kubectl logs -n kube-system <mount-pod-name>
   ```

2. **Shared Mount Not Created**
   ```bash
   # Verify shared mounts are enabled
   kubectl get configmap xpai-csi-config -o yaml
   
   # Check CSI driver environment variables
   kubectl describe pod -n kube-system -l app=csi-nodeplugin-xpai
   ```

3. **Permission Issues**
   ```bash
   # Check shared directory permissions
   ls -ld /var/lib/xpai-csi/shared/
   
   # Verify mount-pod has proper privileges
   kubectl describe pod -n kube-system <mount-pod-name>
   ```

### Debug Commands

```bash
# Check shared mount status
kubectl exec -n kube-system <csi-driver-pod> -- ls -la /var/lib/xpai-csi/shared/

# View mount information
mount | grep xpai-csi

# Check reference counts (if debug endpoint is available)
curl http://localhost:8080/debug/shared-mounts
```

## Limitations

1. **Same Node Only**: Shared mounts only work for PVCs on the same node
2. **Identical Parameters**: Only PVCs with exactly matching StorageClass parameters can share mounts
3. **Filesystem Support**: Not all filesystem types support safe sharing
4. **Cleanup Delay**: Unused shared mounts are cleaned up with a delay to avoid thrashing

## Best Practices

1. **Use Consistent StorageClass Parameters**: Ensure all PVCs that should share use identical parameters
2. **Monitor Resource Usage**: Keep an eye on shared mount directory disk usage
3. **Regular Cleanup**: Configure appropriate cleanup intervals based on your workload patterns
4. **Test Thoroughly**: Verify shared mount behavior with your specific filesystem and workload
5. **Security Considerations**: Ensure shared mounts don't violate your security requirements
