# Cross-Namespace PVC Sharing

## Overview

The XPAI CSI driver supports **cross-namespace PVC sharing**, allowing PVCs with the same name in different namespaces to share the same underlying storage. This feature is particularly useful for multi-tenant applications where different namespaces need access to the same data.

## How It Works

### Traditional Approach
```
Namespace A: PVC "app-data" → NFS:/exports/data/pvc-abc123
Namespace B: PVC "app-data" → NFS:/exports/data/pvc-def456
Namespace C: PVC "app-data" → NFS:/exports/data/pvc-ghi789
```
**Result**: 3 separate directories, no data sharing

### Cross-Namespace Sharing Approach
```
Namespace A: PVC "app-data" ┐
Namespace B: PVC "app-data" ├→ NFS:/exports/data/app-data (shared subdirectory)
Namespace C: PVC "app-data" ┘
```
**Result**: 1 shared directory, all namespaces see the same data

## Configuration

### Enable Cross-Namespace Sharing

In your Helm values:

```yaml
csi:
  crossNamespaceSharing:
    enabled: true
```

### StorageClass Configuration

Add the `enableCrossNamespaceSharing` parameter to your StorageClass:

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-nfs-cross-ns
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/shared-apps"  # Base path
  nfsVersion: "4"
  # Enable cross-namespace sharing
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
```

## Directory Structure

When cross-namespace sharing is enabled, the CSI driver creates subdirectories based on PVC names:

```
NFS Server: /exports/shared-apps/
├── app-data/           # Shared by all "app-data" PVCs
├── database/           # Shared by all "database" PVCs  
├── config/             # Shared by all "config" PVCs
└── logs/               # Shared by all "logs" PVCs
```

## Example Usage

### 1. Create StorageClass

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-data
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/shared"
  enableCrossNamespaceSharing: "true"
```

### 2. Create PVCs in Different Namespaces

```yaml
# In namespace "frontend"
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: frontend
spec:
  storageClassName: shared-data
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi

---
# In namespace "backend" (same PVC name!)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: backend
spec:
  storageClassName: shared-data
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi
```

### 3. Use in Pods

```yaml
# Frontend pod
apiVersion: v1
kind: Pod
metadata:
  name: frontend-app
  namespace: frontend
spec:
  containers:
  - name: app
    image: nginx
    volumeMounts:
    - name: shared-data
      mountPath: /shared
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data

---
# Backend pod (sees same data!)
apiVersion: v1
kind: Pod
metadata:
  name: backend-app
  namespace: backend
spec:
  containers:
  - name: app
    image: python:3.9
    volumeMounts:
    - name: shared-data
      mountPath: /shared
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data
```

## Mount Key Generation

For cross-namespace sharing, the mount key is generated based on:

- PVC name (e.g., "app-data")
- Filesystem type and configuration
- StorageClass parameters

PVCs with the same name and StorageClass will share the same mount key.

## Benefits

1. **Data Sharing**: Multiple namespaces can access the same data
2. **Resource Efficiency**: Single mount-pod for multiple PVCs
3. **Consistency**: All namespaces see the same files and changes
4. **Multi-Tenant Support**: Perfect for microservices architectures

## Use Cases

### 1. Microservices Architecture
```
Frontend Namespace: PVC "shared-config" → /exports/config/shared-config/
Backend Namespace:  PVC "shared-config" → /exports/config/shared-config/
API Namespace:      PVC "shared-config" → /exports/config/shared-config/
```

### 2. Development Environments
```
Dev Namespace:     PVC "project-data" → /exports/projects/project-data/
Test Namespace:    PVC "project-data" → /exports/projects/project-data/
Staging Namespace: PVC "project-data" → /exports/projects/project-data/
```

### 3. Data Processing Pipelines
```
Ingestion Namespace: PVC "pipeline-data" → /exports/data/pipeline-data/
Processing Namespace: PVC "pipeline-data" → /exports/data/pipeline-data/
Output Namespace:    PVC "pipeline-data" → /exports/data/pipeline-data/
```

## Monitoring

### Check Cross-Namespace Mounts

```bash
# List mount pods (should be fewer than PVCs)
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true

# Check CSI driver logs for cross-namespace sharing
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i cross

# Verify shared directory structure on NFS server
ls -la /exports/shared/
```

### Verify Data Sharing

```bash
# Create file in one namespace
kubectl exec -n frontend frontend-app -- touch /shared/test-from-frontend.txt

# Check if visible in another namespace
kubectl exec -n backend backend-app -- ls -la /shared/test-from-frontend.txt
```

## Limitations

1. **Same PVC Name Required**: Only PVCs with identical names can share
2. **Same StorageClass**: Must use the same StorageClass with cross-namespace sharing enabled
3. **Same Node Scheduling**: Works best when pods are on the same node
4. **Filesystem Support**: Only supported for network filesystems (NFS, CephFS, etc.)
5. **Security Considerations**: All sharing namespaces have access to the same data

## Security Considerations

1. **Data Isolation**: Ensure that sharing namespaces should have access to the same data
2. **RBAC**: Use Kubernetes RBAC to control which namespaces can create PVCs with specific names
3. **Network Policies**: Consider network policies to control access between namespaces
4. **Audit**: Monitor cross-namespace access patterns

## Troubleshooting

### Common Issues

1. **PVCs Not Sharing**
   ```bash
   # Check if cross-namespace sharing is enabled
   kubectl get storageclass <storageclass-name> -o yaml | grep enableCrossNamespaceSharing
   
   # Verify PVC names are identical
   kubectl get pvc -A | grep <pvc-name>
   ```

2. **Mount Failures**
   ```bash
   # Check CSI driver logs
   kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins
   
   # Verify StorageClass parameters
   kubectl describe storageclass <storageclass-name>
   ```

3. **Data Not Visible**
   ```bash
   # Check mount points in pods
   kubectl exec -n <namespace> <pod-name> -- mount | grep <mount-path>
   
   # Verify NFS server directory
   ls -la /exports/<base-path>/<pvc-name>/
   ```

## Best Practices

1. **Naming Convention**: Use consistent PVC naming across namespaces
2. **Documentation**: Document which PVCs are shared across namespaces
3. **Testing**: Test cross-namespace sharing in development environments first
4. **Monitoring**: Monitor storage usage and access patterns
5. **Backup**: Ensure shared data is properly backed up
6. **Access Control**: Implement proper RBAC for shared resources
