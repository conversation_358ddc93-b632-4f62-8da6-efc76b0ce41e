# Cross-Namespace PVC Sharing

## Overview

The XPAI CSI driver supports **cross-namespace PVC sharing**, allowing PVCs with the same name in different namespaces to share the same underlying storage. This feature is particularly useful for multi-tenant applications where different namespaces need access to the same data.

## How It Works

### Traditional Approach
```
Namespace A: PVC "app-data" → NFS:/exports/data/pvc-abc123
Namespace B: PVC "app-data" → NFS:/exports/data/pvc-def456
Namespace C: PVC "app-data" → NFS:/exports/data/pvc-ghi789
```
**Result**: 3 separate directories, no data sharing

### Cross-Namespace Sharing Approach (Same StorageClass Required)
```
Namespace A: PVC "app-data" (StorageClass: shared-storage) ┐
Namespace B: PVC "app-data" (StorageClass: shared-storage) ├→ NFS:/exports/data/app-data
Namespace C: PVC "app-data" (StorageClass: shared-storage) ┘
```
**Result**: 1 shared directory, all namespaces see the same data

### StorageClass Isolation (Different StorageClasses)
```
Namespace A: PVC "app-data" (StorageClass: team-a-storage) → NFS:/exports/team-a/app-data
Namespace B: PVC "app-data" (StorageClass: team-b-storage) → NFS:/exports/team-b/app-data
```
**Result**: Separate directories, proper isolation between teams

## Configuration

### Enable Cross-Namespace Sharing

In your Helm values:

```yaml
csi:
  crossNamespaceSharing:
    enabled: true
```

### StorageClass Configuration

Add the `enableCrossNamespaceSharing` parameter to your StorageClass:

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-nfs-cross-ns
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/shared-apps"  # Base path
  nfsVersion: "4"
  # Enable cross-namespace sharing
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
```

## Directory Structure

When cross-namespace sharing is enabled, the CSI driver creates subdirectories based on PVC names:

```
NFS Server: /exports/shared-apps/
├── app-data/           # Shared by all "app-data" PVCs
├── database/           # Shared by all "database" PVCs  
├── config/             # Shared by all "config" PVCs
└── logs/               # Shared by all "logs" PVCs
```

## Example Usage

### 1. Create StorageClass

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: shared-data
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/shared"
  enableCrossNamespaceSharing: "true"
```

### 2. Create PVCs in Different Namespaces

```yaml
# In namespace "frontend"
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: frontend
spec:
  storageClassName: shared-data
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi

---
# In namespace "backend" (same PVC name!)
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: app-data
  namespace: backend
spec:
  storageClassName: shared-data
  accessModes: [ReadWriteMany]
  resources:
    requests:
      storage: 1Gi
```

### 3. Use in Pods

```yaml
# Frontend pod
apiVersion: v1
kind: Pod
metadata:
  name: frontend-app
  namespace: frontend
spec:
  containers:
  - name: app
    image: nginx
    volumeMounts:
    - name: shared-data
      mountPath: /shared
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data

---
# Backend pod (sees same data!)
apiVersion: v1
kind: Pod
metadata:
  name: backend-app
  namespace: backend
spec:
  containers:
  - name: app
    image: python:3.9
    volumeMounts:
    - name: shared-data
      mountPath: /shared
  volumes:
  - name: shared-data
    persistentVolumeClaim:
      claimName: app-data
```

## Mount Key Generation

For cross-namespace sharing, the mount key is generated based on:

- PVC name (e.g., "app-data")
- **StorageClass name** (critical for isolation)
- Filesystem type and configuration
- StorageClass parameters

**Only PVCs with the same name AND same StorageClass will share the same mount key.**

## StorageClass Isolation

Cross-namespace sharing respects StorageClass boundaries to ensure proper multi-tenant isolation:

### Sharing Rules
```yaml
# These PVCs WILL share storage (same name + same StorageClass):
Namespace A: PVC "data" + StorageClass "shared-nfs"  ┐
Namespace B: PVC "data" + StorageClass "shared-nfs"  ├→ SHARED
Namespace C: PVC "data" + StorageClass "shared-nfs"  ┘

# These PVCs will NOT share (different StorageClasses):
Namespace A: PVC "data" + StorageClass "team-a-nfs"  → ISOLATED
Namespace B: PVC "data" + StorageClass "team-b-nfs"  → ISOLATED

# These PVCs will NOT share (different names):
Namespace A: PVC "app-data" + StorageClass "shared-nfs"  → ISOLATED
Namespace B: PVC "db-data"  + StorageClass "shared-nfs"  → ISOLATED
```

### Multi-Tenant Example
```yaml
# Team A's StorageClass
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: team-a-storage
parameters:
  server: "nfs-server"
  path: "/exports/team-a"  # Team A's dedicated path
  enableCrossNamespaceSharing: "true"

# Team B's StorageClass
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: team-b-storage
parameters:
  server: "nfs-server"
  path: "/exports/team-b"  # Team B's dedicated path
  enableCrossNamespaceSharing: "true"
```

Result:
- Team A's PVCs share within `/exports/team-a/`
- Team B's PVCs share within `/exports/team-b/`
- Teams remain completely isolated from each other

## Benefits

1. **Data Sharing**: Multiple namespaces can access the same data
2. **Resource Efficiency**: Single mount-pod for multiple PVCs
3. **Consistency**: All namespaces see the same files and changes
4. **Multi-Tenant Support**: Perfect for microservices architectures

## Use Cases

### 1. Microservices Architecture
```
Frontend Namespace: PVC "shared-config" → /exports/config/shared-config/
Backend Namespace:  PVC "shared-config" → /exports/config/shared-config/
API Namespace:      PVC "shared-config" → /exports/config/shared-config/
```

### 2. Development Environments
```
Dev Namespace:     PVC "project-data" → /exports/projects/project-data/
Test Namespace:    PVC "project-data" → /exports/projects/project-data/
Staging Namespace: PVC "project-data" → /exports/projects/project-data/
```

### 3. Data Processing Pipelines
```
Ingestion Namespace: PVC "pipeline-data" → /exports/data/pipeline-data/
Processing Namespace: PVC "pipeline-data" → /exports/data/pipeline-data/
Output Namespace:    PVC "pipeline-data" → /exports/data/pipeline-data/
```

## Monitoring

### Check Cross-Namespace Mounts

```bash
# List mount pods (should be fewer than PVCs)
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true

# Check CSI driver logs for cross-namespace sharing
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i cross

# Verify shared directory structure on NFS server
ls -la /exports/shared/
```

### Verify Data Sharing

```bash
# Create file in one namespace
kubectl exec -n frontend frontend-app -- touch /shared/test-from-frontend.txt

# Check if visible in another namespace
kubectl exec -n backend backend-app -- ls -la /shared/test-from-frontend.txt
```

## Limitations

1. **Same PVC Name Required**: Only PVCs with identical names can share
2. **Same StorageClass Required**: PVCs MUST use the exact same StorageClass to share storage
   - Different StorageClasses = No sharing (proper isolation)
   - This ensures teams/tenants with different StorageClasses remain isolated
3. **Same Node Scheduling**: Works best when pods are on the same node
4. **Filesystem Support**: Only supported for network filesystems (NFS, CephFS, etc.)
5. **Security Considerations**: All sharing namespaces have access to the same data

## Security Considerations

1. **Data Isolation**: Ensure that sharing namespaces should have access to the same data
2. **RBAC**: Use Kubernetes RBAC to control which namespaces can create PVCs with specific names
3. **Network Policies**: Consider network policies to control access between namespaces
4. **Audit**: Monitor cross-namespace access patterns

## Troubleshooting

### Common Issues

1. **PVCs Not Sharing**
   ```bash
   # Check if cross-namespace sharing is enabled
   kubectl get storageclass <storageclass-name> -o yaml | grep enableCrossNamespaceSharing
   
   # Verify PVC names are identical
   kubectl get pvc -A | grep <pvc-name>
   ```

2. **Mount Failures**
   ```bash
   # Check CSI driver logs
   kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins
   
   # Verify StorageClass parameters
   kubectl describe storageclass <storageclass-name>
   ```

3. **Data Not Visible**
   ```bash
   # Check mount points in pods
   kubectl exec -n <namespace> <pod-name> -- mount | grep <mount-path>
   
   # Verify NFS server directory
   ls -la /exports/<base-path>/<pvc-name>/
   ```

## Best Practices

1. **Naming Convention**: Use consistent PVC naming across namespaces
2. **Documentation**: Document which PVCs are shared across namespaces
3. **Testing**: Test cross-namespace sharing in development environments first
4. **Monitoring**: Monitor storage usage and access patterns
5. **Backup**: Ensure shared data is properly backed up
6. **Access Control**: Implement proper RBAC for shared resources
