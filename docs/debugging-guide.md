# XPAI CSI 驱动调试指南

## 概述

CSI 驱动的调试涉及多个组件和层次，本指南提供了系统性的调试方法和工具。

## 调试环境设置

### 1. 启用详细日志

在 Helm values 中启用调试模式：

```yaml
# values.yaml
csi:
  logLevel: 5  # 最详细的日志级别 (0-5)
  debug:
    enabled: true
    # 启用 pprof 性能分析
    pprof: true
    # 启用 metrics
    metrics: true
```

### 2. 本地开发环境

```bash
# 设置环境变量
export CSI_ENDPOINT=unix:///tmp/csi.sock
export NODE_ID=debug-node
export LOG_LEVEL=5

# 本地运行 CSI 驱动
go run cmd/main.go \
  --endpoint=$CSI_ENDPOINT \
  --nodeid=$NODE_ID \
  --log-level=$LOG_LEVEL \
  --enable-shared-mounts=true \
  --enable-cross-namespace-sharing=true
```

## 调试工具和方法

### 1. 日志分析

#### 查看 CSI 驱动日志
```bash
# Node Plugin 日志
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins -f

# Controller Plugin 日志  
kubectl logs -n kube-system -l app=csi-controller-xpai -c plugins -f

# 过滤特定操作
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i "NodeStageVolume\|NodePublishVolume"

# 过滤跨 namespace 共享相关日志
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i "cross-namespace\|shared"
```

#### 查看 kubelet 日志
```bash
# 在节点上查看 kubelet 日志
journalctl -u kubelet -f | grep -i csi

# 查看 CSI 相关的 kubelet 日志
journalctl -u kubelet | grep "xpai-csi"
```

### 2. CSI 状态检查

#### 检查 CSI 驱动注册状态
```bash
# 检查 CSIDriver 对象
kubectl get csidriver

# 检查 CSINode 对象
kubectl get csinode

# 详细查看 CSI 驱动信息
kubectl describe csidriver fuse.csi.xiaoshiai.cn
```

#### 检查 PV/PVC 状态
```bash
# 检查 PVC 状态
kubectl get pvc -A

# 检查 PV 详情
kubectl get pv -o wide

# 查看 PVC 事件
kubectl describe pvc <pvc-name> -n <namespace>

# 查看 PV 详情
kubectl describe pv <pv-name>
```

### 3. 挂载点调试

#### 检查节点上的挂载点
```bash
# 在节点上检查挂载点
mount | grep xpai-csi

# 检查共享挂载目录
ls -la /var/lib/xpai-csi/shared/

# 检查 kubelet 工作目录
ls -la /var/lib/kubelet/pods/*/volumes/kubernetes.io~csi/
```

#### 检查 mount-pod 状态
```bash
# 查看 mount-pod
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true

# 查看 mount-pod 日志
kubectl logs -n kube-system <mount-pod-name>

# 进入 mount-pod 调试
kubectl exec -n kube-system <mount-pod-name> -it -- /bin/sh
```

## 常见问题调试

### 1. PVC 一直处于 Pending 状态

```bash
# 检查 StorageClass
kubectl describe storageclass <storageclass-name>

# 检查 CSI 驱动是否运行
kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai

# 检查 PVC 事件
kubectl describe pvc <pvc-name> -n <namespace>

# 检查 controller 日志中的 CreateVolume 调用
kubectl logs -n kube-system -l app=csi-controller-xpai | grep CreateVolume
```

### 2. Pod 无法启动，卷挂载失败

```bash
# 检查 Pod 事件
kubectl describe pod <pod-name> -n <namespace>

# 检查 node plugin 日志
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep <volume-id>

# 检查节点上的挂载状态
kubectl exec -n kube-system <csi-node-pod> -- mount | grep <volume-id>
```

### 3. 跨 namespace 共享不工作

```bash
# 检查 StorageClass 参数
kubectl get storageclass <storageclass-name> -o yaml | grep enableCrossNamespaceSharing

# 检查 volume ID 是否相同
kubectl get pv -o jsonpath='{.items[*].spec.csi.volumeHandle}' | tr ' ' '\n' | sort

# 检查共享挂载日志
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i "cross-namespace\|shared"

# 检查 mount-pod 数量
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true --no-headers | wc -l
```

## 性能调试

### 1. 启用 pprof

```yaml
# 在 DaemonSet 中添加 pprof 端口
ports:
- name: pprof
  containerPort: 6060
  protocol: TCP
```

```bash
# 访问 pprof 接口
kubectl port-forward -n kube-system <csi-pod> 6060:6060

# 在另一个终端中分析性能
go tool pprof http://localhost:6060/debug/pprof/profile
```

### 2. 监控指标

```bash
# 检查共享挂载指标
kubectl exec -n kube-system <csi-pod> -- curl localhost:8080/metrics | grep xpai_csi
```

## 网络文件系统调试

### 1. NFS 调试

```bash
# 在 mount-pod 中测试 NFS 连接
kubectl exec -n kube-system <mount-pod> -- showmount -e <nfs-server>

# 手动挂载测试
kubectl exec -n kube-system <mount-pod> -- mount -t nfs4 <nfs-server>:<path> /tmp/test

# 检查 NFS 挂载选项
kubectl exec -n kube-system <mount-pod> -- mount | grep nfs
```

### 2. CephFS 调试

```bash
# 检查 Ceph 集群状态
kubectl exec -n kube-system <mount-pod> -- ceph -s

# 测试 CephFS 挂载
kubectl exec -n kube-system <mount-pod> -- mount -t ceph <monitors>:/ /tmp/test -o name=<user>,secret=<secret>
```

## 开发调试技巧

### 1. 使用 delve 调试器

```bash
# 安装 delve
go install github.com/go-delve/delve/cmd/dlv@latest

# 调试 CSI 驱动
dlv debug cmd/main.go -- \
  --endpoint=unix:///tmp/csi.sock \
  --nodeid=debug-node \
  --log-level=5
```

### 2. 单元测试

```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test ./pkg/filesystem -v

# 运行带覆盖率的测试
go test ./... -coverprofile=coverage.out
go tool cover -html=coverage.out
```

### 3. 集成测试

```bash
# 使用 kind 创建测试集群
kind create cluster --config test/kind-config.yaml

# 部署 CSI 驱动
helm install xpai-csi deploy/xpai-csi --namespace kube-system

# 运行集成测试
./scripts/test-cross-namespace-sharing.sh
```

## 故障排除检查清单

### CSI 驱动启动问题
- [ ] 检查 RBAC 权限
- [ ] 检查 CSI socket 路径
- [ ] 检查节点标签和选择器
- [ ] 检查镜像拉取策略

### 卷创建问题
- [ ] 检查 StorageClass 参数
- [ ] 检查 CSI 驱动注册
- [ ] 检查 controller 日志
- [ ] 检查网络连接

### 卷挂载问题
- [ ] 检查节点 plugin 状态
- [ ] 检查挂载点权限
- [ ] 检查网络文件系统连接
- [ ] 检查 mount-pod 状态

### 跨 namespace 共享问题
- [ ] 检查 StorageClass 配置
- [ ] 检查 PVC 名称一致性
- [ ] 检查 volume ID 生成
- [ ] 检查共享挂载逻辑

## 日志级别说明

- **Level 0**: 只有错误信息
- **Level 1**: 错误和警告
- **Level 2**: 基本操作信息
- **Level 3**: 详细操作信息
- **Level 4**: 调试信息
- **Level 5**: 最详细的调试信息（包括请求/响应）

## 有用的调试命令

```bash
# 一键检查 CSI 驱动状态
kubectl get pods,csidriver,csinode,storageclass -A | grep -i xpai

# 检查所有相关资源
kubectl get pv,pvc,pods -A -o wide | grep -E "(xpai|Pending|Error)"

# 导出所有 CSI 相关日志
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins > csi-node.log
kubectl logs -n kube-system -l app=csi-controller-xpai -c plugins > csi-controller.log
```
