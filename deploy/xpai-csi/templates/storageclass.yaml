{{- if .Values.csi.createExampleStorageClasses }}
---
# NFS StorageClass Example
{{- if .Values.csi.filesystems.nfs.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-nfs
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI NFS StorageClass"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: nfs
  server: "{{ .Values.csi.examples.nfs.server | default "YOUR_NFS_SERVER" }}"
  path: "{{ .Values.csi.examples.nfs.path | default "/exports/data" }}"
  nfsVersion: "{{ .Values.csi.filesystems.nfs.defaultVersion }}"
  mountFlags: "{{ .Values.csi.filesystems.nfs.defaultOptions }}"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}

---
# CephFS StorageClass Example
{{- if .Values.csi.filesystems.cephfs.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-cephfs
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI CephFS StorageClass"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: cephfs
  monitors: "{{ .Values.csi.examples.cephfs.monitors | default "YOUR_CEPH_MONITORS" }}"
  user: "{{ .Values.csi.filesystems.cephfs.defaultUser }}"
  secretRef: "{{ .Values.csi.examples.cephfs.secretRef | default "ceph-secret" }}"
  rootPath: "{{ .Values.csi.examples.cephfs.rootPath | default "/" }}"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}

---
# JuiceFS StorageClass Example
{{- if .Values.csi.filesystems.juicefs.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-juicefs
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI JuiceFS StorageClass"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: juicefs
  metaURL: "{{ .Values.csi.examples.juicefs.metaURL | default "YOUR_JUICEFS_META_URL" }}"
  bucket: "{{ .Values.csi.examples.juicefs.bucket | default "YOUR_S3_BUCKET" }}"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}

---
# GlusterFS StorageClass Example
{{- if .Values.csi.filesystems.glusterfs.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-glusterfs
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI GlusterFS StorageClass"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: glusterfs
  endpoints: "{{ .Values.csi.examples.glusterfs.endpoints | default "YOUR_GLUSTER_ENDPOINTS" }}"
  volume: "{{ .Values.csi.examples.glusterfs.volume | default "gv0" }}"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}

---
# Generic FUSE StorageClass Example
{{- if .Values.csi.filesystems.fuse.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-fuse
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI Generic FUSE StorageClass"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: fuse
  command: "{{ .Values.csi.examples.fuse.command | default "sshfs" }}"
  args: "{{ .Values.csi.examples.fuse.args | default "user@server:/remote/path" }}"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}

---
# Cross-Namespace Sharing NFS StorageClass Example
{{- if and .Values.csi.filesystems.nfs.enabled .Values.csi.crossNamespaceSharing.enabled }}
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: {{ include "xpai-csi.fullname" . }}-nfs-cross-ns
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
    description: "XPAI CSI NFS StorageClass with Cross-Namespace Sharing"
provisioner: {{ .Values.csi.driverName }}
parameters:
  type: nfs
  server: "{{ .Values.csi.examples.nfs.server | default "YOUR_NFS_SERVER" }}"
  path: "{{ .Values.csi.examples.nfs.path | default "/exports/shared" }}"
  nfsVersion: "{{ .Values.csi.filesystems.nfs.defaultVersion }}"
  mountFlags: "{{ .Values.csi.filesystems.nfs.defaultOptions }}"
  # Enable cross-namespace sharing for PVCs with same name
  enableCrossNamespaceSharing: "true"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
{{- end }}
{{- end }}
