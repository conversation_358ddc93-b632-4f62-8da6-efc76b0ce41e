apiVersion: v1
kind: ConfigMap
metadata:
  name: xpai-csi-config-{{ .Release.Name }}
  namespace: {{ include "xpai-csi.namespace" . }}
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  # Driver configuration
  driver.yaml: |
    driverName: {{ .Values.csi.driverName }}
    version: {{ .Chart.AppVersion | default "1.0.0" }}
    nodeID: ${NODE_ID}
    
    # Mount pod configuration
    mountPod:
      namespace: {{ .Values.csi.mountPod.namespace }}
      image: {{ .Values.csi.mountPod.image }}
      timeout: {{ .Values.csi.mountPod.timeout }}
      resources:
        {{- toYaml .Values.csi.mountPod.resources | nindent 8 }}
    
    # Health checker configuration
    healthChecker:
      enabled: {{ .Values.csi.healthChecker.enabled }}
      checkInterval: {{ .Values.csi.healthChecker.checkInterval }}
      maxRetries: {{ .Values.csi.healthChecker.maxRetries }}
      detailedCheck: {{ .Values.csi.healthChecker.detailedCheck }}
    
    # Filesystem configurations
    filesystems:
      {{- if .Values.csi.filesystems.nfs.enabled }}
      nfs:
        enabled: true
        defaultVersion: {{ .Values.csi.filesystems.nfs.defaultVersion }}
        defaultOptions: {{ .Values.csi.filesystems.nfs.defaultOptions }}
      {{- end }}
      
      {{- if .Values.csi.filesystems.cephfs.enabled }}
      cephfs:
        enabled: true
        defaultUser: {{ .Values.csi.filesystems.cephfs.defaultUser }}
      {{- end }}
      
      {{- if .Values.csi.filesystems.juicefs.enabled }}
      juicefs:
        enabled: true
        image: {{ .Values.csi.filesystems.juicefs.image }}
      {{- end }}
      
      {{- if .Values.csi.filesystems.glusterfs.enabled }}
      glusterfs:
        enabled: true
      {{- end }}
      
      {{- if .Values.csi.filesystems.fuse.enabled }}
      fuse:
        enabled: true
      {{- end }}

  # Example storage class configurations
  examples.yaml: |
    # NFS StorageClass Example
    apiVersion: storage.k8s.io/v1
    kind: StorageClass
    metadata:
      name: xpai-nfs
    provisioner: {{ .Values.csi.driverName }}
    parameters:
      type: nfs
      server: "YOUR_NFS_SERVER"
      path: "/exports/data"
      nfsVersion: "{{ .Values.csi.filesystems.nfs.defaultVersion }}"
      mountFlags: "{{ .Values.csi.filesystems.nfs.defaultOptions }}"
    reclaimPolicy: Delete
    volumeBindingMode: WaitForFirstConsumer
    allowVolumeExpansion: true
    
    ---
    # CephFS StorageClass Example
    apiVersion: storage.k8s.io/v1
    kind: StorageClass
    metadata:
      name: xpai-cephfs
    provisioner: {{ .Values.csi.driverName }}
    parameters:
      type: cephfs
      monitors: "YOUR_CEPH_MONITORS"
      user: "{{ .Values.csi.filesystems.cephfs.defaultUser }}"
      secretRef: "ceph-secret"
      rootPath: "/"
    reclaimPolicy: Delete
    volumeBindingMode: WaitForFirstConsumer
    allowVolumeExpansion: true
    
    ---
    # JuiceFS StorageClass Example
    apiVersion: storage.k8s.io/v1
    kind: StorageClass
    metadata:
      name: xpai-juicefs
    provisioner: {{ .Values.csi.driverName }}
    parameters:
      type: juicefs
      metaURL: "YOUR_JUICEFS_META_URL"
      bucket: "YOUR_S3_BUCKET"
    reclaimPolicy: Delete
    volumeBindingMode: WaitForFirstConsumer
    allowVolumeExpansion: true

  # Troubleshooting guide
  troubleshooting.md: |
    # XPAI CSI Driver Troubleshooting Guide
    
    ## Common Issues
    
    ### 1. Mount Pod Failures
    - Check mount pod logs: `kubectl logs -n {{ .Values.csi.mountPod.namespace }} <mount-pod-name>`
    - Verify filesystem server connectivity
    - Check storage class parameters
    
    ### 2. Permission Issues
    - Ensure CSI driver has proper RBAC permissions
    - Check if mount pod namespace exists
    - Verify filesystem server permissions
    
    ### 3. Health Check Failures
    - Check health checker logs in CSI driver pod
    - Verify mount point accessibility
    - Check filesystem server status
    
    ## Debugging Commands
    
    ```bash
    # Check CSI driver pods
    kubectl get pods -n {{ include "xpai-csi.namespace" . }} -l app=csi-nodeplugin-xpai
    
    # Check CSI driver logs
    kubectl logs -n {{ include "xpai-csi.namespace" . }} -l app=csi-nodeplugin-xpai -c plugins
    
    # Check mount pods
    kubectl get pods -n {{ .Values.csi.mountPod.namespace }} -l xpai-csi/mount-pod=true
    
    # Check storage classes
    kubectl get storageclass -l provisioner={{ .Values.csi.driverName }}
    
    # Check PVCs
    kubectl get pvc -A
    ```
    
    ## Configuration Validation
    
    Use the following command to validate your configuration:
    ```bash
    kubectl get configmap xpai-csi-config-{{ .Release.Name }} -n {{ include "xpai-csi.namespace" . }} -o yaml
    ```
