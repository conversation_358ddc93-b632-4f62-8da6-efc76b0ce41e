---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: xpai-csi-{{ .Release.Name }}
  namespace: {{ include "xpai-csi.namespace" . }}
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: xpai-csi-plugin-{{ .Release.Name }}
rules:
  # CSI volume operations
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["persistentvolumes/status"]
    verbs: ["get", "patch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims/status"]
    verbs: ["get", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]

  # Mount pod operations
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["create", "delete", "get", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods/status"]
    verbs: ["get", "watch"]
  - apiGroups: [""]
    resources: ["pods/log"]
    verbs: ["get"]

  # Node operations
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["nodes/status"]
    verbs: ["get"]

  # Secret operations (for filesystem credentials)
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "list"]

  # ConfigMap operations (for filesystem configuration)
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list"]

  # Storage class operations
  - apiGroups: ["storage.k8s.io"]
    resources: ["storageclasses"]
    verbs: ["get", "list", "watch"]

  # CSI operations
  - apiGroups: ["storage.k8s.io"]
    resources: ["csinodes"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["storage.k8s.io"]
    resources: ["volumeattachments"]
    verbs: ["get", "list", "watch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: xpai-csi-plugin-{{ .Release.Name }}
subjects:
  - kind: ServiceAccount
    name: xpai-csi-{{ .Release.Name }}
    namespace: {{ include "xpai-csi.namespace" . }}
roleRef:
  kind: ClusterRole
  name: xpai-csi-plugin-{{ .Release.Name }}
  apiGroup: rbac.authorization.k8s.io