---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: xpai-csi-{{ .Release.Name }}
  namespace: {{ include "xpai-csi.namespace" . }}
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: xpai-csi-plugin-{{ .Release.Name }}
rules:
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["persistentvolumes/status"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["persistentvolumeclaims/status"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: xpai-csi-plugin-{{ .Release.Name }}
subjects:
  - kind: ServiceAccount
    name: xpai-csi-{{ .Release.Name }}
    namespace: {{ include "xpai-csi.namespace" . }}
roleRef:
  kind: ClusterRole
  name: xpai-csi-plugin-{{ .Release.Name }}
  apiGroup: rbac.authorization.k8s.io