kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: csi-nodeplugin-xpai-{{ .Release.Name }}
  namespace: {{ include "xpai-csi.namespace" . }}
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: csi-nodeplugin-xpai
  template:
    metadata:
      labels:
        app: csi-nodeplugin-xpai
    spec:
      {{- with .Values.image.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: xpai-csi-{{ .Release.Name }}
      {{- if .Values.csi.tolerations }}
      tolerations:
{{ toYaml .Values.csi.tolerations | indent 6 }}
      {{- end }}
      priorityClassName: system-node-critical
      {{- if .Values.csi.config.hostNetwork }}
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      {{- end }}
      hostPID: {{ .Values.csi.hostPID }}
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: type
                operator: NotIn
                values:
                - virtual-kubelet
      containers:
      - name: node-driver-registrar
        image: "{{ .Values.csi.registrar.image }}"
        args:
          - --v=5
          - --csi-address={{ .Values.csi.kubelet.rootDir }}/csi-plugins/fuse.csi.xiaoshiai.cn/csi.sock
          - --kubelet-registration-path={{ .Values.csi.kubelet.rootDir }}/csi-plugins/fuse.csi.xiaoshiai.cn/csi.sock
        env:
          - name: KUBE_NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
        volumeMounts:
          - name: kubelet-dir
            mountPath: {{ .Values.csi.kubelet.rootDir }}
            mountPropagation: "HostToContainer"
          - name: registration-dir
            mountPath: /registration
      - name: plugins
        securityContext:
          privileged: true
          runAsUser: 0
          capabilities:
            add: ["SYS_ADMIN"]
          allowPrivilegeEscalation: true
        image: "{{ .Values.csi.plugins.image }}"
        command: ["/opt/bin/entrypoint.sh"]
        args:
          - "--nodeid=$(NODE_ID)"
          - "--endpoint=$(CSI_ENDPOINT)"
          - --v=5
        env:
          - name: NODE_ID
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          - name: NODE_IP
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
          {{- if .Values.csi.recoverWarningThreshold }}
          - name: REVOCER_WARNING_THRESHOLD
            value: {{ .Values.csi.recoverWarningThreshold | quote}}
          {{- end }}
          - name: ALLOW_PATCH_STALE_NODE
            value: "true"
          - name: KUBELET_ROOTDIR
            value: {{ .Values.csi.kubelet.rootDir }}
          - name: CSI_ENDPOINT
            value: unix://{{ .Values.csi.kubelet.rootDir }}/csi-plugins/fuse.csi.xiaoshiai.cn/csi.sock
          - name: NODEPUBLISH_METHOD
            value: {{ .Values.csi.nodePublishMethod }}
        imagePullPolicy: "Always"
        volumeMounts:
          - name: plugin-dir
            mountPath: /plugin
          - name: kubelet-dir
            mountPath: {{ .Values.csi.kubelet.rootDir }}
            mountPropagation: "Bidirectional"
          - name: kubelet-kube-config
            mountPath: /etc/kubernetes/kubelet.conf
            mountPropagation: "HostToContainer"
            readOnly: true
          # Check subdirectory to avoid looping bind mounts
          {{- $kubeletRootDir := ternary ( .Values.csi.kubelet.rootDir ) ( print .Values.csi.kubelet.rootDir "/" ) ( hasSuffix "/" .Values.csi.kubelet.rootDir ) }}
          {{- if not ( hasPrefix $kubeletRootDir .Values.csi.kubelet.certDir ) }}
          - name: kubelet-cert-dir
            mountPath: {{ .Values.csi.kubelet.certDir | quote }}
            readOnly: true
          {{- end }}
      volumes:
        - name: kubelet-dir
          hostPath:
            path: {{ .Values.csi.kubelet.rootDir | quote }}
            type: Directory
        {{- if not ( hasPrefix $kubeletRootDir .Values.csi.kubelet.certDir ) }}
        - name: kubelet-cert-dir
          hostPath:
            path: {{ .Values.csi.kubelet.certDir | quote }}
            type: Directory
        {{- end }}
        - name: plugin-dir
          hostPath:
            path: {{ .Values.csi.kubelet.rootDir }}/plugins/csi-xpai-plugin
            type: DirectoryOrCreate
        - hostPath:
            path: {{ .Values.csi.kubelet.rootDir }}/plugins_registry
            type: DirectoryOrCreate
          name: registration-dir
        - hostPath:
            path: {{ .Values.csi.kubelet.kubeConfigFile | quote }}
            type: File
          name: kubelet-kube-config
