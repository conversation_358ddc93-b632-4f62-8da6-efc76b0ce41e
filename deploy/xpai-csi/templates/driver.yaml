apiVersion: {{ ternary "storage.k8s.io/v1" "storage.k8s.io/v1beta1" (semverCompare ">=1.18.0-0" .Capabilities.KubeVersion.Version) }}
kind: CSIDriver
metadata:
  name: {{ .Values.csi.driverName }}
  labels:
    app.kubernetes.io/name: {{ include "xpai-csi.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
spec:
  attachRequired: false
  podInfoOnMount: true
  volumeLifecycleModes:
    - Persistent
    - Ephemeral
  {{- if semverCompare ">=1.19.0-0" .Capabilities.KubeVersion.Version }}
  fsGroupPolicy: File
  {{- end }}
  {{- if semverCompare ">=1.22.0-0" .Capabilities.KubeVersion.Version }}
  requiresRepublish: false
  {{- end }}