# XPAI CSI Driver

Universal CSI driver for multiple filesystem types with mount-pod support.

## Overview

The XPAI CSI driver provides a unified interface for mounting various filesystem types in Kubernetes clusters. It supports:

- **NFS** - Network File System
- **CephFS** - Ceph Filesystem
- **JuiceFS** - Cloud-native distributed filesystem
- **GlusterFS** - Scale-out network-attached storage
- **Generic FUSE** - Any FUSE-based filesystem

## Key Features

- **Mount-Pod Architecture**: Uses dedicated pods for mounting operations, ensuring reliability and isolation
- **Health Checking**: Continuous monitoring of mount points with automatic recovery
- **Multi-Filesystem Support**: Single driver for multiple filesystem types
- **Flexible Configuration**: Extensive configuration options through StorageClass parameters
- **Production Ready**: Includes proper RBAC, resource limits, and monitoring

## Installation

### Prerequisites

- Kubernetes 1.18+
- Helm 3.0+
- Appropriate filesystem servers (NFS, Ceph, etc.)

### Install with <PERSON><PERSON>

```bash
# Add the repository (if available)
helm repo add xpai-csi https://charts.kubegems.io/xpai-csi
helm repo update

# Install the CSI driver
helm install xpai-csi xpai-csi/xpai-csi \
  --namespace kube-system \
  --create-namespace
```

### Install from Source

```bash
# Clone the repository
git clone https://github.com/kubegems/xpai-csi.git
cd xpai-csi

# Install using Helm
helm install xpai-csi deploy/xpai-csi \
  --namespace kube-system \
  --create-namespace
```

## Configuration

### Basic Configuration

The driver can be configured through the `values.yaml` file:

```yaml
csi:
  # Enable/disable specific filesystems
  filesystems:
    nfs:
      enabled: true
      defaultVersion: "4"
    cephfs:
      enabled: true
    juicefs:
      enabled: false
  
  # Health checking
  healthChecker:
    enabled: true
    checkInterval: 30s
    maxRetries: 3
  
  # Mount pod configuration
  mountPod:
    namespace: kube-system
    timeout: 300s
```

### StorageClass Examples

#### NFS StorageClass

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: xpai-nfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: nfs
  server: "192.168.1.100"
  path: "/exports/data"
  nfsVersion: "4"
  mountFlags: "hard,intr"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
```

#### CephFS StorageClass

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: xpai-cephfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
  type: cephfs
  monitors: "192.168.1.10:6789,192.168.1.11:6789"
  user: "admin"
  secretRef: "ceph-secret"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
```

## Usage

### Create a PVC

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: my-pvc
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: xpai-nfs
```

### Use in a Pod

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: test-pod
spec:
  containers:
  - name: test
    image: busybox
    command: ["sleep", "3600"]
    volumeMounts:
    - name: data
      mountPath: /data
  volumes:
  - name: data
    persistentVolumeClaim:
      claimName: my-pvc
```

## Monitoring and Troubleshooting

### Check Driver Status

```bash
# Check CSI driver pods
kubectl get pods -n kube-system -l app=csi-nodeplugin-xpai

# Check driver logs
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins
```

### Check Mount Pods

```bash
# List mount pods
kubectl get pods -n kube-system -l xpai-csi/mount-pod=true

# Check mount pod logs
kubectl logs -n kube-system <mount-pod-name>
```

### Health Status

The driver includes built-in health checking. Check the logs for health status:

```bash
kubectl logs -n kube-system -l app=csi-nodeplugin-xpai -c plugins | grep -i health
```

## Configuration Reference

See the [values.yaml](values.yaml) file for complete configuration options.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the Apache License 2.0 - see the LICENSE file for details.

## Support

For support and questions:
- GitHub Issues: https://github.com/kubegems/xpai-csi/issues
- Documentation: https://github.com/kubegems/xpai-csi/docs
- Email: <EMAIL>
