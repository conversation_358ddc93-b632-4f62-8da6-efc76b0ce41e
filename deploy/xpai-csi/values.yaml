# Global image configuration
image:
  imagePullSecrets: []
  registry: registry.cn-beijing.aliyuncs.com
  repository: kubegems/xpai-csi
  tag: v0.0.2
  pullPolicy: Always

# CSI Driver configuration
csi:
  # Driver name - should match the driver name in code
  driverName: fuse.csi.xiaoshiai.cn

  # Node configuration
  node:
    tolerations:
      - operator: Exists
    hostNetwork: false
    hostPID: false
    priorityClassName: system-node-critical

    # Resource limits for CSI node plugin
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 128Mi

  # Node driver registrar sidecar
  registrar:
    image: registry.k8s.io/sig-storage/csi-node-driver-registrar:v2.13.0
    resources:
      limits:
        cpu: 100m
        memory: 100Mi
      requests:
        cpu: 10m
        memory: 20Mi

  # Kubelet configuration
  kubelet:
    kubeConfigFile: /etc/kubernetes/kubelet.conf
    certDir: /var/lib/kubelet/pki
    rootDir: /var/lib/kubelet

  # Mount pod configuration
  mountPod:
    # Namespace where mount pods will be created
    namespace: kube-system
    # Default image for mount operations
    image: registry.cn-beijing.aliyuncs.com/kubegems/xpai-csi:v0.0.2
    # Timeout for mount operations
    timeout: 300s
    # Resource limits for mount pods
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 128Mi

  # Shared mount configuration
  sharedMounts:
    # Enable shared mounts for same StorageClass on same node
    enabled: true
    # Directory for shared mounts on the host
    sharedDir: /var/lib/xpai-csi/shared
    # Cleanup interval for stale shared mounts
    cleanupInterval: 1h

  # Health checker configuration
  healthChecker:
    enabled: true
    checkInterval: 30s
    maxRetries: 3
    # Create health check pods for detailed checks
    detailedCheck: false

  # Filesystem adapter configuration
  filesystems:
    # NFS configuration
    nfs:
      enabled: true
      defaultVersion: "4"
      defaultOptions: "hard,intr,timeo=600,retrans=2"

    # CephFS configuration
    cephfs:
      enabled: true
      defaultUser: "admin"

    # JuiceFS configuration
    juicefs:
      enabled: true
      image: juicedata/juicefs-csi-driver:latest

    # GlusterFS configuration
    glusterfs:
      enabled: true

    # Generic FUSE configuration
    fuse:
      enabled: true

  # Legacy configuration (for backward compatibility)
  recoverWarningThreshold: 50
  nodePublishMethod: bindMount

  # Create example storage classes
  createExampleStorageClasses: false

  # Example configurations for storage classes
  examples:
    nfs:
      server: "192.168.1.100"
      path: "/exports/data"

    cephfs:
      monitors: "192.168.1.10:6789,192.168.1.11:6789,192.168.1.12:6789"
      secretRef: "ceph-secret"
      rootPath: "/"

    juicefs:
      metaURL: "redis://redis.default.svc.cluster.local:6379/1"
      bucket: "s3://my-bucket"

    glusterfs:
      endpoints: "192.168.1.20:24007,192.168.1.21:24007"
      volume: "gv0"

    fuse:
      command: "sshfs"
      args: "user@server:/remote/path"
