image:
  imagePullSecrets: []


csi:
  tolerations:
    - operator: Exists
  config:
    hostNetwork: false
  registrar:
    image: registry.k8s.io/sig-storage/csi-node-driver-registrar:v2.13.0
  plugins:
    image: registry.cn-beijing.aliyuncs.com/kubegems/xpai-csi:v0.0.1
  kubelet:
    kubeConfigFile: /etc/kubernetes/kubelet.conf
    certDir: /var/lib/kubelet/pki
    rootDir: /var/lib/kubelet
  recoverWarningThreshold: 50
  # default method is "bindMount", "symlink" is also support
  # Notice: if use nodePublishMethod symlink, fuse recovery is not support
  nodePublishMethod: bindMount
  hostPID: false
