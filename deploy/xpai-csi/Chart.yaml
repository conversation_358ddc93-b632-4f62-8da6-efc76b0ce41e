apiVersion: v2
name: xpai-csi
description: Universal CSI driver for multiple filesystem types with mount-pod support

# This is a CSI driver that supports multiple filesystem types including NFS, CephFS,
# JuiceFS, GlusterFS, and generic FUSE filesystems. It uses mount-pod approach for
# reliable filesystem mounting and includes health checking capabilities.
type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.2.0

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "0.0.2"

# Keywords for easier discovery
keywords:
  - csi
  - storage
  - filesystem
  - nfs
  - cephfs
  - juicefs
  - glusterfs
  - fuse
  - mount-pod

# Maintainers information
maintainers:
  - name: KubeGems Team
    email: <EMAIL>

# Home page and sources
home: https://github.com/kubegems/xpai-csi
sources:
  - https://github.com/kubegems/xpai-csi

# Annotations for additional metadata
annotations:
  category: Storage
  licenses: Apache-2.0
