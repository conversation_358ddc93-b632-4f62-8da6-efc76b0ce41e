PWD := $(shell pwd)
GOPATH := $(shell go env GOPATH)
GOARCH := $(shell go env GOARCH)
GOOS := $(shell go env GOOS)
GOPACKAGE=$(shell go list -m)
BUILD_DATE=$(shell date -u +'%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(shell git rev-parse HEAD)



ldflags+=-w -s
# ldflags+=-X '${GOPACKAGE}/pkg/version.gitVersion=${GIT_VERSION}'
ldflags+=-X '${GOPACKAGE}/pkg/version.gitCommit=${GIT_COMMIT}'
ldflags+=-X '${GOPACKAGE}/pkg/version.buildDate=${BUILD_DATE}'


BIN_DIR?=bin
IMAGE_REGISTRY?=registry.cn-beijing.aliyuncs.com

define go-build
	CGO_ENABLED=0 GOOS=$(1) GOARCH=$(2) go build -gcflags=all="-N -l" -ldflags="${ldflags}" -o $(PWD)/${BIN_DIR}/xpai-csi-$(2) cmd/main.go
endef

build:
	- mkdir -p ${BIN_DIR}
	$(call go-build,linux,amd64)
	$(call go-build,linux,arm64)

PLATFORM?=linux/amd64,linux/arm64
release-image:
	cp scripts/entrypoint.sh ${BIN_DIR}/entrypoint.sh
	cp scripts/check_bind_mounts.sh ${BIN_DIR}/check_bind_mounts.sh
	cp scripts/check_mount.sh ${BIN_DIR}/check_mount.sh
	docker buildx build --platform=${PLATFORM} --push -t ${IMAGE_REGISTRY}/kubegems/xpai-csi:v0.0.1 -f Dockerfile ${BIN_DIR}

# 调试和开发相关命令
NAMESPACE ?= kube-system
DEBUG_ENDPOINT ?= unix:///tmp/csi.sock
DEBUG_NODE_ID ?= debug-node
LOG_LEVEL ?= 5

.PHONY: help
help: ## 显示帮助信息
	@echo "XPAI CSI 驱动开发工具"
	@echo ""
	@echo "构建命令:"
	@echo "  build              构建二进制文件"
	@echo "  release-image      构建并推送 Docker 镜像"
	@echo ""
	@echo "调试命令:"
	@echo "  debug-status       检查 CSI 驱动状态"
	@echo "  debug-logs         查看 CSI 驱动日志"
	@echo "  debug-pods         检查 Pod 和 PVC 状态"
	@echo "  debug-mounts       检查挂载点状态"
	@echo "  debug-cross-ns     检查跨 namespace 共享状态"
	@echo "  debug-all          执行所有调试检查"
	@echo "  collect-logs       收集所有日志到文件"
	@echo ""
	@echo "测试命令:"
	@echo "  test               运行单元测试"
	@echo "  test-cross-ns      测试跨 namespace 共享功能"
	@echo "  test-controller    测试 controller server 功能"
	@echo "  cleanup-tests      清理测试资源"
	@echo ""
	@echo "开发命令:"
	@echo "  dev-run            本地运行 CSI 驱动"
	@echo "  debug              使用 delve 调试器运行"
	@echo "  logs               快速查看日志"
	@echo "  status             快速查看状态"

.PHONY: test
test: ## 运行单元测试
	@echo "运行单元测试..."
	go test -v ./...

.PHONY: dev-run
dev-run: build ## 本地运行 CSI 驱动 (开发模式)
	@echo "本地运行 CSI 驱动..."
	sudo $(PWD)/${BIN_DIR}/xpai-csi-amd64 \
		--endpoint=$(DEBUG_ENDPOINT) \
		--nodeid=$(DEBUG_NODE_ID) \
		--log-level=$(LOG_LEVEL) \
		--enable-shared-mounts=true \
		--enable-cross-namespace-sharing=true

.PHONY: debug
debug: ## 使用 delve 调试器运行
	@echo "启动调试器..."
	dlv debug cmd/main.go -- \
		--endpoint=$(DEBUG_ENDPOINT) \
		--nodeid=$(DEBUG_NODE_ID) \
		--log-level=$(LOG_LEVEL)

# 调试和监控命令
.PHONY: debug-status
debug-status: ## 检查 CSI 驱动状态
	@./scripts/debug-csi.sh --status

.PHONY: debug-logs
debug-logs: ## 查看 CSI 驱动日志
	@./scripts/debug-csi.sh --logs

.PHONY: debug-pods
debug-pods: ## 检查 Pod 和 PVC 状态
	@./scripts/debug-csi.sh --pods

.PHONY: debug-mounts
debug-mounts: ## 检查挂载点状态
	@./scripts/debug-csi.sh --mounts

.PHONY: debug-cross-ns
debug-cross-ns: ## 检查跨 namespace 共享状态
	@./scripts/debug-csi.sh --cross-ns

.PHONY: debug-all
debug-all: ## 执行所有调试检查
	@./scripts/debug-csi.sh --all

.PHONY: collect-logs
collect-logs: ## 收集所有日志到文件
	@./scripts/debug-csi.sh --collect-logs

# 测试相关命令
.PHONY: test-cross-ns
test-cross-ns: ## 测试跨 namespace 共享功能
	@echo "测试跨 namespace 共享..."
	kubectl apply -f examples/cross-namespace-sharing-demo.yaml
	@echo "等待 30 秒让 Pod 启动..."
	sleep 30
	@./scripts/debug-csi.sh --cross-ns

.PHONY: test-controller
test-controller: ## 测试 controller server 功能
	@echo "测试 controller server..."
	kubectl apply -f examples/controller-test.yaml
	@echo "等待 30 秒让 Pod 启动..."
	sleep 30
	@./scripts/debug-csi.sh --pods

.PHONY: cleanup-tests
cleanup-tests: ## 清理测试资源
	@echo "清理测试资源..."
	kubectl delete -f examples/cross-namespace-sharing-demo.yaml --ignore-not-found=true
	kubectl delete -f examples/controller-test.yaml --ignore-not-found=true
	kubectl delete -f examples/storageclass-isolation-demo.yaml --ignore-not-found=true

# 快速命令
.PHONY: logs
logs: ## 快速查看日志
	@kubectl logs -n $(NAMESPACE) -l app=csi-nodeplugin-xpai -c plugins -f

.PHONY: status
status: ## 快速查看状态
	@kubectl get pods,pvc,pv -A | grep -E "(xpai|Pending|Error|NAME)"

