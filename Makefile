PWD := $(shell pwd)
GOPATH := $(shell go env GOPATH)
GOARCH := $(shell go env GOARCH)
GOOS := $(shell go env GOOS)
GOPACKAGE=$(shell go list -m)
BUILD_DATE=$(shell date -u +'%Y-%m-%d_%H:%M:%S')
GIT_COMMIT=$(shell git rev-parse HEAD)



ldflags+=-w -s
# ldflags+=-X '${GOPACKAGE}/pkg/version.gitVersion=${GIT_VERSION}'
ldflags+=-X '${GOPACKAGE}/pkg/version.gitCommit=${GIT_COMMIT}'
ldflags+=-X '${GOPACKAGE}/pkg/version.buildDate=${BUILD_DATE}'


BIN_DIR?=bin
IMAGE_REGISTRY?=registry.cn-beijing.aliyuncs.com

define go-build
	CGO_ENABLED=0 GOOS=$(1) GOARCH=$(2) go build -gcflags=all="-N -l" -ldflags="${ldflags}" -o $(PWD)/${BIN_DIR}/xpai-csi-$(2) cmd/main.go
endef

build:
	- mkdir -p ${BIN_DIR}
	$(call go-build,linux,amd64)
	$(call go-build,linux,arm64)

PLATFORM?=linux/amd64,linux/arm64
release-image:
	cp scripts/entrypoint.sh ${BIN_DIR}/entrypoint.sh
	cp scripts/check_bind_mounts.sh ${BIN_DIR}/check_bind_mounts.sh
	cp scripts/check_mount.sh ${BIN_DIR}/check_mount.sh
	docker buildx build --platform=${PLATFORM} --push -t ${IMAGE_REGISTRY}/kubegems/xpai-csi:v0.0.1 -f Dockerfile ${BIN_DIR}

