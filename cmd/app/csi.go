package app

import (
	"flag"
	"fmt"
	"os"

	"github.com/kubegems/xpai-csi/pkg"
	"github.com/spf13/cobra"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/clientcmd"
	ctrl "sigs.k8s.io/controller-runtime"
)

const (
	group   = "data.xiaoshiai.cn"
	version = "v1"
)

var (
	//groupVersion  = schema.GroupVersion{Group: group, Version: version}
	//schemeBuilder = &scheme.Builder{GroupVersion: groupVersion}
	//addToScheme   = schemeBuilder.AddToScheme
	sch                   = runtime.NewScheme()
	endpoint              string
	nodeID                string
	kubeletKubeConfigPath string
)

var startCmd = &cobra.Command{
	Use:   "start",
	Short: "start xpai driver on node",
	Run: func(cmd *cobra.Command, args []string) {
		handle()
	},
}

func init() {
	_ = clientgoscheme.AddToScheme(sch)
	//addToScheme(sch)
	if err := flag.Set("logtostderr", "true"); err != nil {
		fmt.Printf("Failed to flag.set due to %v", err)
		os.Exit(1)
	}

	startCmd.Flags().StringVarP(&nodeID, "nodeid", "", "", "node id")
	if err := startCmd.MarkFlagRequired("nodeid"); err != nil {
		ErrorAndExit(err)
	}

	startCmd.Flags().StringVarP(&endpoint, "endpoint", "", "", "CSI endpoint")
	if err := startCmd.MarkFlagRequired("endpoint"); err != nil {
		ErrorAndExit(err)
	}
	startCmd.Flags().StringVarP(&kubeletKubeConfigPath, "kubelet-kube-config", "", "/etc/kubernetes/kubelet.conf", "The file path to kubelet kube config")
	startCmd.Flags().AddGoFlagSet(flag.CommandLine)
}

func handle() {
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme: sch,
	})
	if err != nil {
		panic(fmt.Sprintf("csi: unable to create controller manager due to error %v", err))
	}
	config, err := clientcmd.BuildConfigFromFlags("", kubeletKubeConfigPath)
	if err != nil {
		panic(fmt.Sprintf("csi: unable to build kubelet config from %s due to error %v", kubeletKubeConfigPath, err))
	}

	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		panic(fmt.Sprintf("csi: unable to build client-go client from kubelet kubeconfig %s due to error %v", kubeletKubeConfigPath, err))
	}
	csiDriver := pkg.NewDriver(nodeID, endpoint, mgr.GetClient(), mgr.GetAPIReader(), client)
	if err := mgr.Add(csiDriver); err != nil {
		panic(fmt.Sprintf("csi: unable to add driver to controller manager due to error %v", err))
	}
	ctx := ctrl.SetupSignalHandler()
	if err = mgr.Start(ctx); err != nil {
		panic(fmt.Sprintf("unable to start controller recover due to error %v", err))
	}

}

func ErrorAndExit(err error) {
	fmt.Fprintf(os.Stderr, "%s", err.Error())
	os.Exit(1)
}
