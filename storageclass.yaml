---
# NFS StorageClass Example
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
   name: xpai-nfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
    type: nfs
    server: "192.168.1.100"
    path: "/exports/data"
    nfsVersion: "4"
    mountFlags: "hard,intr"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# CephFS StorageClass Example
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
   name: xpai-cephfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
    type: cephfs
    monitors: "192.168.1.10:6789,192.168.1.11:6789,192.168.1.12:6789"
    user: "admin"
    secretRef: "ceph-secret"
    rootPath: "/"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# Juice<PERSON> StorageClass Example
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
   name: xpai-juicefs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
    type: juicefs
    metaURL: "redis://redis.default.svc.cluster.local:6379/1"
    bucket: "s3://my-bucket"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# GlusterFS StorageClass Example
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
   name: xpai-glusterfs
provisioner: fuse.csi.xiaoshiai.cn
parameters:
    type: glusterfs
    endpoints: "192.168.1.20:24007,192.168.1.21:24007"
    volume: "gv0"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true

---
# Generic FUSE StorageClass Example
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
   name: xpai-fuse
provisioner: fuse.csi.xiaoshiai.cn
parameters:
    type: fuse
    command: "sshfs"
    args: "user@server:/remote/path"
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowVolumeExpansion: true
