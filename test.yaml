---
# Namespace for testing
apiVersion: v1
kind: Namespace
metadata:
  name: xpai-test

---
# NFS PVC Example
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: xpai-nfs-pvc
  namespace: xpai-test
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: xpai-nfs

---
# CephFS PVC Example
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: xpai-cephfs-pvc
  namespace: xpai-test
spec:
  accessModes:
  - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: xpai-cephfs

---
# Test Pod using NFS
apiVersion: v1
kind: Pod
metadata:
  name: test-nfs-pod
  namespace: xpai-test
spec:
  restartPolicy: OnFailure
  containers:
  - args:
    - -c
    - |
      echo "Testing NFS mount..."
      df -h /data
      echo "Creating test file..."
      echo "Hello from NFS!" > /data/test-nfs.txt
      cat /data/test-nfs.txt
      echo "NFS test completed successfully!"
      tail -f /dev/null
    command:
    - /bin/sh
    image: busybox:latest
    name: test-container
    volumeMounts:
    - mountPath: /data
      name: nfs-volume
  volumes:
  - name: nfs-volume
    persistentVolumeClaim:
      claimName: xpai-nfs-pvc

---
# Test Pod using CephFS
apiVersion: v1
kind: Pod
metadata:
  name: test-cephfs-pod
  namespace: xpai-test
spec:
  restartPolicy: OnFailure
  containers:
  - args:
    - -c
    - |
      echo "Testing CephFS mount..."
      df -h /data
      echo "Creating test file..."
      echo "Hello from CephFS!" > /data/test-cephfs.txt
      cat /data/test-cephfs.txt
      echo "CephFS test completed successfully!"
      tail -f /dev/null
    command:
    - /bin/sh
    image: busybox:latest
    name: test-container
    volumeMounts:
    - mountPath: /data
      name: cephfs-volume
  volumes:
  - name: cephfs-volume
    persistentVolumeClaim:
      claimName: xpai-cephfs-pvc