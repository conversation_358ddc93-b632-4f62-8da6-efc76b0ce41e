---
# apiVersion: v1
# kind: PersistentVolumeClaim
# metadata:
#   name: xpai
#   namespace: xpai-test
# spec:
#   accessModes:
#   - ReadWriteMany
#   resources:
#     requests:
#       storage: 1Gi
#   storageClassName: xpai-fuse-test
# ---
apiVersion: v1
kind: Pod
metadata:
  name: busybox
  namespace: xpai-test
spec:
  restartPolicy: OnFailure
  containers:
  - args:
    - -c
    - tail -f /dev/null
    command:
    - /bin/sh
    image: library/busybox:latest
    name: busybox
    volumeMounts:
    - mountPath: /data
      name: test-pv
  volumes:
  - name: test-pv
    persistentVolumeClaim:
      claimName: xpai