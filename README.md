通用的CSI
1.每个计算集群都会部署一套该CSI和一个DataSet的controller
2.controller监听DataSet这个CR,
DataSet中的定义如下:
{
    Name:"abc",
    StorageCluster:{ 存储集群的配置
        xxxxx
    },
    Quota: 11111,
    Permission: read-write/readonly
}

## Architecture

The XPAI CSI driver consists of:

- **Node Plugin**: Handles volume mounting/unmounting on worker nodes with shared mount optimization
- **Controller Plugin**: Manages volume lifecycle with cross-namespace sharing support
- **Identity Plugin**: Provides driver information and capabilities

### Controller Server Features

- **Cross-Namespace Volume ID Generation**: Creates consistent volume IDs for PVCs with same name and StorageClass
- **Multi-Access Mode Support**: Supports ReadWriteOnce, ReadWriteMany, and ReadOnlyMany access modes
- **Volume Context Management**: Adds metadata for cross-namespace sharing tracking
- **StorageClass Validation**: Ensures proper isolation between different StorageClasses

### Cross-Namespace Sharing

The controller server implements intelligent volume ID generation for cross-namespace sharing:

1. **Same PVC Name + Same StorageClass** → Same Volume ID (sharing enabled)
2. **Same PVC Name + Different StorageClass** → Different Volume IDs (isolation maintained)
3. **Different PVC Names** → Different Volume IDs (no sharing)

This ensures proper multi-tenant isolation while enabling efficient resource sharing where appropriate.
